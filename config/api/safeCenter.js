import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function certAdd(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/cert/add', data).then((res) => {
      reslove(res);
    });
  });
}

export function getCertDetail() {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/cert/detail').then((res) => {
      reslove(res);
    });
  });
}

export function initFaceVerify(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/member/cert/initFaceVerify', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function describeFaceVerify() {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/cert/describeFaceVerify').then((res) => {
      reslove(res);
    });
  });
}
