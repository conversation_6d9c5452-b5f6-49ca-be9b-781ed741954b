import request from '../../common/request.js';
import localConfig from '../config';
import util from '@/utils/util';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function searchProductList2(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(
        webUrl.replace('mall-portal', 'mall-search') + '/kkSearch/search',
        data,
        { ext: params },
      )
      .then((res) => {
        reslove(res);
      });
  });
}

export function search(data = {}) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/product/search', data).then((res) => {
      reslove(res);
    });
  });
}

export function getProductAttribute(id, params) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + '/home/<USER>/attrInfo2/' + id, params)
      .then((res) => {
        reslove(res);
      });
  });
}

export function productCategoryNames(params) {
  return new Promise((reslove, reject) => {
    request
      .get(
        webUrl.replace('mall-portal', 'mall-search') +
          '/kkSearch/search/relate',
        params,
      )
      .then((res) => {
        reslove(res);
      });
  });
}
