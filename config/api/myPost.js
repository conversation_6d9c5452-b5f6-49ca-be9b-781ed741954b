import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getProductList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/product/list', params).then((res) => {
      reslove(res);
    });
  });
}

export function publishStatus(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/member/product/update/publishStatus', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function updatePrice(id, params) {
  return new Promise((reslove, reject) => {
    request
      .post(
        webUrl + `/member/product/updatePrice/${id}`,
        {},
        {
          ext: params,
        },
      )
      .then((res) => {
        reslove(res);
      });
  });
}

export function deleteProduct(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + `/member/product/delete/${id}`).then((res) => {
      reslove(res);
    });
  });
}

// export function unVerify(id) {
//   return new Promise((reslove, reject) => {
//     request.get(webUrl + `/product/unVerify/${id}`).then((res) => {
//       reslove(res);
//     });
//   });
// }

export function publishUp(params) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + `/member/product/update/publishUp`, params)
      .then((res) => {
        reslove(res);
      });
  });
}

export function publishDown(params) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + `/member/product/update/publishDown`, params)
      .then((res) => {
        reslove(res);
      });
  });
}

export function sameSoldProduct(params) {
  return new Promise((reslove, reject) => {
    request
      .get(
        webUrl.replace('mall-portal', 'mall-search') +
          `/kkSearch/sameSoldProduct`,
        params,
      )
      .then((res) => {
        reslove(res);
      });
  });
}

export function sameProductList(params) {
  return new Promise((reslove, reject) => {
    request
      .get(
        webUrl.replace('mall-portal', 'mall-search') +
          `/kkSearch/sameProductList`,
        params,
      )
      .then((res) => {
        reslove(res);
      });
  });
}
