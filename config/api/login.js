import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function loginCodenumApi(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/sso/smsLogin', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function loginPwdnumApi(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/sso/login', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function sendPhoneCode(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/sso/getAuthCode', params).then((res) => {
      reslove(res);
    });
  });
}

export function deletAccountApi() {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/sso/delete').then((res) => {
      reslove(res);
    });
  });
}

export function logout() {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/sso/logout').then((res) => {
      reslove(res);
    });
  });
}
