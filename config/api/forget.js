import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function updatePwdApi(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/sso/updatePassword2', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function updateInfo(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/sso/updateInfo', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}
