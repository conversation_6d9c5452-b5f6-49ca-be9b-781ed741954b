import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getDetail(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/product/detail/' + id).then((res) => {
      reslove(res);
    });
  });
}

export function getDetailByCode(params) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + '/product/detail', params)
      .then((res) => {
        reslove(res);
      });
  });
}

export function getPreview(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/product/preview/' + id).then((res) => {
      reslove(res);
    });
  });
}

export function productCollectionAdd(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/productCollection/add', data).then((res) => {
      reslove(res);
    });
  });
}

export function getProductCollectionList(params) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + '/member/productCollection/list', params)
      .then((res) => {
        reslove(res);
      });
  });
}

export function productCollectionDetele(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/member/productCollection/delete', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function readHistoryCreate(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/readHistory/create', data).then((res) => {
      reslove(res);
    });
  });
}

export function getReadHistoryList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/readHistory/list', params).then((res) => {
      reslove(res);
    });
  });
}

export function readHistoryDelete(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/member/readHistory/delete', data, { ext: params })
      .then((res) => {
        reslove(res);
      });
  });
}

export function readHistoryClear(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/member/readHistory/clear', data, { ext: params })
      .then((res) => {
        reslove(res);
      });
  });
}

export function getSkinAndHero(id) {
  return new Promise((reslove, reject) => {
    request
      .get(`https://images2.kkzhw.com/mall/statics/wzry/${id}.json`, {
        noerrorshow: 1,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function sendSm(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/product/smsCode', data).then((res) => {
      reslove(res);
    });
  });
}

export function startLuhao(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/product/startLuhao', data).then((res) => {
      reslove(res);
    });
  });
}

export function topOfferPrice(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/negotia/topOfferPrice2', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}
