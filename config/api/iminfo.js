import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;
import isLogin from '@/common/mixins/isLogin';
export function getImInfo() {
  if (!isLogin()) {
    return Promise.reject(false);
  }
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/sso/iminfo').then((res) => {
      reslove(res);
    });
  });
}
