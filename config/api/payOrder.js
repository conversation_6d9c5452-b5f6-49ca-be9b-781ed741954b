import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getOrderDetail(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/order/detail/' + id).then((res) => {
      reslove(res);
    });
  });
}

// export function paySuccess(params, data = {}) {
//   return new Promise((reslove, reject) => {
//     request
//       .post(webUrl + '/order/paySuccess', data, { ext: params })
//       .then((res) => {
//         reslove(res);
//       });
//   });
// }

export function payCheck(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/order/payCheck', params).then((res) => {
      reslove(res);
    });
  });
}

export function getOrderTeam(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/order/orderTeam?orderId=' + id).then((res) => {
      reslove(res);
    });
  });
}

// H5微信支付
export function pay(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/kkpay/pay', params).then((res) => {
      reslove(res);
    });
  });
}

// H5支付宝支付
export function payAliH5(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/kkpay/payH5', params).then((res) => {
      reslove(res);
    });
  });
}


// PC支付宝支付
export function payPcTurnPay(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/kkpay/pcTurnPay', params).then((res) => {
      reslove(res);
    });
  });
}