import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getProductAttribute(skuId) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + '/home/<USER>/listall/' + skuId)
      .then((res) => {
        reslove(res);
      });
  });
}

export function productCreate(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/product/create', data).then((res) => {
      reslove(res);
    });
  });
}

export function productUpdate(id, data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/product/update/' + id, data).then((res) => {
      reslove(res);
    });
  });
}

export function getProductCategory(productCategoryId) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + '/home/<USER>/' + productCategoryId)
      .then((res) => {
        reslove(res);
      });
  });
}

export function getUpdateInfo(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/product/updateInfo/' + id).then((res) => {
      reslove(res);
    });
  });
}

export function productUpdateQuestion(id, data) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/member/product/updateQuestion/' + id, data)
      .then((res) => {
        reslove(res);
      });
  });
}

export function startLuhao2(data) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/record/task/start', data)
      .then((res) => {
        reslove(res);
      });
  });
}

export function recordTaskDetail(id) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + `/record/task/${id}`)
      .then((res) => {
        reslove(res);
      });
  });
}
export function taskUpdate(data) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/record/task/update', data)
      .then((res) => {
        reslove(res);
      });
  });
}

export function recordTaskSmsCode(data) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/record/task/smsCode', data)
      .then((res) => {
        reslove(res);
      });
  });
}