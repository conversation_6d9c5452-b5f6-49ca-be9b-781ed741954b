import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getVerionInfo(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/home/<USER>/android/verionInfo', data).then((res) => {
      reslove(res);
    });
  });
}

export function getVerionInfo2(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/home/<USER>/android/verionInfo2', data).then((res) => {
      reslove(res);
    });
  });
}
export function mpConfig(data) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/openapi/weixin/mpConfig', data).then((res) => {
      reslove(res);
    });
  });
}