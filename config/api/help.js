import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

/**
 * 获取首页列表
 */

export function getHelpList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>/list', params).then((res) => {
      reslove(res);
    });
  });
}

export function getHelpDetail(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>/detail/' + id).then((res) => {
      reslove(res);
    });
  });
}
