import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function memberStatics(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/statics', data).then((res) => {
      reslove(res);
    });
  });
}

export function getWxQrcode(data) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/get_wx_qrcode', data).then((res) => {
      reslove(res);
    });
  });
}
export function unbindWeixin(data) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/unbindWeixin', data).then((res) => {
      reslove(res);
    });
  });
}

export function generatescheme(data) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/wxa/generatescheme', data).then((res) => {
      reslove(res);
    });
  });
}
