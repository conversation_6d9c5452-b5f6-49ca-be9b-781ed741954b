import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getKfList(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/home/<USER>/list', data).then((res) => {
      reslove(res);
    });
  });
}

export function getZBList(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/home/<USER>/list', data).then((res) => {
      reslove(res);
    });
  });
}

export function getBigMemberList(params) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + '/home/<USER>/bigMember', params)
      .then((res) => {
        reslove(res);
      });
  });
}

export function kfCheck(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>/checkKF', params).then((res) => {
      reslove(res);
    });
  });
}

export function checkBU(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>/checkBU', params).then((res) => {
      reslove(res);
    });
  });
}

export function checkSK(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>/checkSK', params).then((res) => {
      reslove(res);
    });
  });
}

export function getTeamBigMember(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/kkim/team/recovery', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function getMemberHisKFList(params) {
  return new Promise((reslove, reject) => {
    request
      .get(webUrl + '/kkim/m2kf/getMemberHisKFList', params)
      .then((res) => {
        reslove(res);
      });
  });
}

export function m2kfTalk(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/kkim/m2kf/talk', params).then((res) => {
      reslove(res);
    });
  });
}

export function m2kfSendProduct(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/kkim/m2kf/sendProduct', params).then((res) => {
      reslove(res);
    });
  });
}

export function m2kfSendOrder(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/kkim/m2kf/sendOrder', params).then((res) => {
      reslove(res);
    });
  });
}

export function getSellerForminfo(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/sso/getSellerForminfo', data).then((res) => {
      reslove(res);
    });
  });
}

export function savaSellerForm(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/sso/savaSellerForm', data).then((res) => {
      reslove(res);
    });
  });
}

export function getCategoryAdverList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}
export function getCategoryAdverList2(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}

export function getFlowState(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/order/flow/detail', params).then((res) => {
      reslove(res);
    });
  });
}

export function orderTeam(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/product/orderTeam', params).then((res) => {
      reslove(res);
    });
  });
}

export function reportAdd(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/report/add', data).then((res) => {
      reslove(res);
    });
  });
}

export function changeKfList(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/member/changeKfList', data).then((res) => {
      reslove(res);
    });
  });
}

export function detailBySn(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/product/detailBySn', params).then((res) => {
      reslove(res);
    });
  });
}
export function negotiaSellerNegoSet(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/negotia/sellerNegoSet', params).then((res) => {
      reslove(res);
    });
  });
}
