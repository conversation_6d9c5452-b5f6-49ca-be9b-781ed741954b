import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function generateKKConfirmOrder(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/order/generateKKConfirmOrder', data).then((res) => {
      reslove(res);
    });
  });
}

export function generateKKOrder2(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/order/generateKKOrder2', data).then((res) => {
      reslove(res);
    });
  });
}

export function myOrderList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/order/list', params).then((res) => {
      reslove(res);
    });
  });
}

export function mySellerList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/order/sellerList', params).then((res) => {
      reslove(res);
    });
  });
}

export function getOrderDetail(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/order/detail/' + id).then((res) => {
      reslove(res);
    });
  });
}

export function cancelUserOrder(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/order/cancelUserOrder', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function deleteOrder(orderId) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + `/order/deleteOrder/${orderId}`).then((res) => {
      reslove(res);
    });
  });
}

export function payConfig(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/member/payConfig').then((res) => {
      reslove(res);
    });
  });
}

export function getOrderSku(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>/getOrderSku/'+id).then((res) => {
      reslove(res);
    });
  });
}

export function productConsultationRecords(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/product/consultationRecords',params).then((res) => {
      reslove(res);
    });
  });
}
export function freeNegoOffer(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/negotia/freeNegoOffer', params).then((res) => {
      reslove(res);
    });
  });
}