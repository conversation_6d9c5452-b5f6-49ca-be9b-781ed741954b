import request from '../../common/request.js';
import localConfig from '../config';
const webUrlKK = localConfig.HTTP_REQUEST_URL_KK;

/**
 * 获取用户信息
 */
export function getUserinforApi(o) {
  return new Promise((reslove, reject) => {
    request.get(webUrlKK + '/sso/info').then((res) => {
      reslove(res);
    });
  });
}

/**
 * 上传获取临时token
 */
export function stsTokenApi(filePath) {
  return new Promise((reslove, reject) => {
    request.get(webUrlKK + '/member/oss/policy', { filePath }).then((res) => {
      reslove(res);
    });
  });
}
