import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getBuyerList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/negotia/BuyerList', params).then((res) => {
      reslove(res);
    });
  });
}

export function negotiaCancel(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/negotia/cancel', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function generateKKConfirmOrderMyAssess(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/negotia/generateKKConfirmOrder', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function generateKKOrderMyAssess2(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/negotia/generateKKOrder2', params).then((res) => {
      reslove(res);
    });
  });
}

export function offerPrice(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/negotia/buyerOfferPrice', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function getSellerList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/negotia/SellerList', params).then((res) => {
      reslove(res);
    });
  });
}

export function sellerDo(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/negotia/sellerDo', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function sellerOfferPrice(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/negotia/sellerOfferPrice', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function negotiaDelete(id) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + `/negotia/delete/${id}`).then((res) => {
      reslove(res);
    });
  });
}
export function getNegotiaDetail(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + `/negotia/detailByTeam?tid=${id}`).then((res) => {
      reslove(res);
    });
  });
}