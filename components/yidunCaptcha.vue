<template>
  <view> </view>
</template>

<script>
export default {
  props: {
    isSense: false,
    captchaId: String,
    width: {
      type: [String, Number],
      default: 320,
    },
    lang: {
      type: String,
      default: 'zh-CN',
    },
    customStyles: {
      type: Object,
      default: () => ({}),
    },
    timeout: {
      type: Number,
      default: 12000,
    }, // 客户端初始化超时时长
    dimAmount: Number, // 仅限客户端
    isToutchOutsideDisappear: Boolean, // 仅限客户端
    isHideCloseBtn: Boolean, // 仅限客户端
    useDefaultFallback: {
      type: Boolean,
      defaultL: true,
    }, // 降级仅限客户端，h5端若要使用降级，请接入降级方案，参考官网
    failedMaxRetryCount: {
      type: Number,
      default: 3,
    }, // 降级配置,仅限客户端
  },
  data() {
    return {
      captchaIns: null,
      options: null,
    };
  },
  created() {
    this.initNeCaptcha();
  },
  destroyed() {
    this.captchaIns = null;
  },
  methods: {
    initNeCaptcha() {
      // #ifdef APP-PLUS
      let appCustom = {};
      if (this.customStyles && this.customStyles.icon) {
        const { slider } = this.customStyles.icon;
        appCustom = {
          'control_bar_start_url': slider,
          'control_bar_moving_url': slider,
          'control_bar_error_url': slider,
        };
      }
      this.captchaIns = uni.requireNativePlugin('YD-Captcha');
      this.captchaIns.init({
        ...appCustom,
        'captcha_id': this.captchaId,
        'language_type': this.lang,
        'dimAmount': 0.6,
        'is_touch_outside_disappear': true,
        'timeout': this.timeout,
        'is_hide_close_button': false,
        'use_default_fallback': true,
        'failed_max_retry_count': 4,
        'is_debug': true,
        'is_show_loading': true,
        'is_close_button_bottom': true,
        'size': 'small',
        'styleConfig': {
          'radius': 10,
          'capBarTextColor': '#25D4D0',
          'capBarTextSize': 18,
          'capBarTextWeight': 'bold',
          'borderColor': '#25D4D0',
          'borderRadius': 10,
          'backgroundMoving': '#DC143C',
          'executeBorderRadius': 10,
          'executeBackground': '#DC143C',
          'capBarTextAlign': 'center',
          'capPaddingTop': 10,
          'capPaddingRight': 10,
          'capPaddingBottom': 10,
          'capPaddingLeft': 10,
          'paddingTop': 15,
          'paddingBottom': 15,
        },
      });
      // #endif
    },
    verify() {
      // #ifdef APP-PLUS
      this.captchaIns.showCaptcha((data) => {
        var method = data.method;
        if (method == 'onSuccess') {
          // 验证成功
          var data = data.data;
          this.$emit('verify', null, data);
        } else if (method == 'onError') {
          var data = data.data;
          this.$emit('verify', data, null);
        }
      });
      // #endif
    },
    reset() {
      // app 不需要 reset，直接调用 showCaptcha 即可重新开始验证流程
      // #ifdef APP-PLUS
      this.verify();
      // #endif
    },
    destroyCap() {
      // #ifdef APP-PLUS
      this.captchaIns.destroyCaptcha();
      // #endif
    },
  },
};
</script>

<style></style>
