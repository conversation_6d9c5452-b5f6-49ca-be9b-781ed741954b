<template>
  <TipPanel
    v-if="shokfPop"
    :has-cancel="false"
    :has-confirm="false"
    @cancel="shokfPop = false"
  >
    <view class="wx_title">扫码添加客服微信</view>
    <view class="spaceCenter wx_qrcode">
      <canvas
        :style="{ width: `${qrcodeSize}px`, height: `${qrcodeSize}px` }"
        canvas-id="qrcode_kf"
      />
    </view>
    <view class="line_between"></view>
    <view class="wxnote">添加客服微信进行咨询</view>
    <view class="wxnote_txt">微信号：{{ kfObj['微信号'] }}</view>
  </TipPanel>
</template>
<script>
import { getMemberHisKFList, getKfList } from '@/config/api/kf.js';
import uQRCode from '@/uni_modules/cc-defineQRCode/components/cc-defineQRCode/common/uqrcode.js';
import isLogin from '@/common/mixins/isLogin';
import ChatMixin from '@/common/mixins/chat';

export default {
  mixins: [ChatMixin],
  data() {
    return {
      shokfPop: false,

      kfObj: {},
      imUniLoading: false,
      qrcodeSize: 60,
    };
  },

  mounted(){
  },
  methods: {
    getKfObj(game) {
      this.kfObj ={}
      return getKfList({
        game,
        type: '咨询客服',
      }).then((res) => {
        if (res.code == 200) {
          let list = res.data || [];
          if (list.length) {
            this.kfObj = _.sample(list);
          }
        }
      });
    },
    createWx(url) {
      uQRCode.make({
        canvasId: 'qrcode_kf',
        text: url,
        size: this.qrcodeSize,
        margin: 0,
      });
    },
    // 进入客服界面，供外部调用
    async goChat({ productCategoryId, productId,game } = {}) {
      if (!isLogin()) {
        uni.navigateTo({
          url: '/pages/login/login?isBack=1',
        });
        return;
      }
      if (this.imUniLoading) {
        return;
      }
      try {
        await this.getKfObj(game)
      } catch (error) {
        this.kfObj={}
      }
      console.log('客服信息：',this.kfObj)
      
      // 微信客服展示二维码
      if (this.kfObj.wxurl && this.kfObj['微信号']) {
        this.shokfPop = true
        this.$nextTick(() => {
          this.createWx(this.kfObj.wxurl);
        });
      } else {
        // 系统客服进入页面
        this.imUniLoading = true;
        uni.showLoading({
          title: '加载中',
          mask: true,
        });

        Promise.all([
          getMemberHisKFList({
            cateId: productCategoryId,
            productId: productId || '',
          }),
        ])
          .then((values) => {
            const res = values[0];
            if (res.code == 200) {
              let findKf = res.data;
              this.goChatPage({
                imcode: findKf,
                flag: 'p2p',
                productCategoryId,
                productId,
              });
            }
          })
          .finally(() => {
            uni.hideLoading();
            setTimeout(() => {
              this.imUniLoading = false;
            }, 34);
          });
      }
    },
	getParamsFromUrl(url) {
	  return Object.fromEntries(new URL(url).searchParams.entries());
	},
	async goChat2(url) {
	    if (!isLogin()) {
	      uni.navigateTo({
	        url: '/pages/login/login?isBack=1',
	      });
	      return;
	    }
		let params = this.getParamsFromUrl(url);
		if (!params.hasOwnProperty('cateId')) {
			return;
		}
	    if (this.imUniLoading) {
	      return;
	    }
	      // 系统客服进入页面
	      this.imUniLoading = true;
	      uni.showLoading({
	        title: '加载中',
	        mask: true,
	      });
	
	      Promise.all([
	        getMemberHisKFList({
	          cateId: params.cateId,
	          productId:  '',
	        }),
	      ])
	        .then((values) => {
	          const res = values[0];
	          if (res.code == 200) {
	            let findKf = res.data;
	            this.goChatPage({
	              imcode: findKf,
	              flag: 'p2p',
	              productCategoryId:params.cateId,
	            });
	          }
	        })
	        .finally(() => {
	          uni.hideLoading();
	          setTimeout(() => {
	            this.imUniLoading = false;
	          }, 34);
	        });
	    
	  },
	},
  
  
};
</script>
<style lang="scss" scoped>
.wx_title {
  color: #FF720C;
  font-family: YouSheBiaoTiHei;
  font-size: 44rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 76rpx;
  text-align: center;
}
.wx_qrcode{
  border: 1px solid  #FFB74A;
  width: fit-content;
  height: fit-content;
  margin:0 auto;
}
.line_between{
  width: 320rpx;
  height: 2rpx;
  background: #FFEED5;
  margin: 20rpx auto 26rpx auto;
}
.wxnote {
  color: #1B1B1B;
  text-align: center;
  font-family: YouSheBiaoTiHei;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}
.wxnote_txt{
  margin-top: 8rpx;
  color: #969696;
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  text-align: center;
}
</style>
