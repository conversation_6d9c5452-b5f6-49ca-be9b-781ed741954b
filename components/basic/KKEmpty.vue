<template>
  <view class="kk-epmty">
    <image
      src="../../assets/imgs/empty_logo.svg"
      style="width:80rpx;margin-right:14rpx"
      mode="widthFix"
    />
    <view v-if="content" class="gradient-primary content">{{content}}</view>
    <view v-else>
      <image src="../../assets/imgs/empty_text.png" mode="widthFix" style="width:104rpx;height:auto"/>
      <view class="text">暂时没有搜索到您要的数据</view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'GradientBorder',
  props: {
    content: {
      type: String,
      default: '', // pink blue
    },
  },
  data() {
    return {
      keyword: '',
    };
  },
  watch: {},
  mounted() {},
  methods: {
    doSearch() {
      this.$emit('search', this.keyword);
    },
  },
};
</script>

<style lang="scss" scoped>
.kk-epmty{
  display: flex;
  align-items: center;
  padding-left: 140rpx;
  height: 272rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  background: #fff;
}
.text{
  color:$uni-color-paragraph;
  font-size: 20rpx;
  font-weight: 500;
}

.content{
  font-family: YouSheBiaoTiHei;
  font-size: 40rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 42rpx;
  width: 300rpx;
}
</style>
