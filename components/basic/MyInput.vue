<template>
  <view class="input_box spaceBetween">
    <view class="spaceStart">
      <IconFont v-if="icon" :icon="icon" :size="18" class="input_icon" />
      <input
        :type="pwdType"
        v-model="localV"
        :placeholder="placeholder"
        style="border: none"
      />
    </view>
    <IconFont v-if="isPsw" :size="18" :icon="pwdType === 'password'?'hidden':'see'" class="input_psw_icon" @click="showPwd"/>
  </view>
</template>
<script>
export default {
  props: {
    isPsw: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '请输入',
    },
    icon: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      pwdType:'text',
      localV: '',
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        this.localV = v;
      },
    },
    localV(v) {
      this.$emit('update', v);
    },
  },
  mounted(){
    this.pwdType = this.isPsw?'password':'text'
  },
  methods: {
    // 登录密码框展示密码
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = 'text';
      } else {
        this.pwdType = 'password';
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.input_box {
  width: 100%;
  box-sizing: border-box;
  padding:20rpx 30rpx;
  margin-bottom: 40rpx;

  border-radius: 100rpx;
  border: 1rpx solid  #969696;

  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.input_icon{
  width: 44rpx;
  height: 44rpx;
  margin-right: 30rpx;
}

input::focus{
  border: none;
}
</style>
