<template>
  <view
    :class="[type, size]"
    style="display: flex; align-items: center; width: 100%"
  >
    <image
      v-if="hasLogo"
      src="../../assets/imgs/logo.svg"
      style="width: 70rpx; margin-right: 20rpx"
      mode="widthFix"
      @click="loginWrap"
    ></image>
    <slot name="left"></slot>
    <GradientBorder
      :round="60"
      :border-size="size === 'small' ? 1 : 2"
      class="search-border"
    >
      <view class="search-box" @click="handelSearcBoxClick">
        <view v-if="hasYx" class="select-name" @click="handelChooseGame"
          >{{ yxName }}
          <IconFont
            :size="10"
            icon="arrow-down"
            style="margin-left: 12rpx; color: rgba(0, 0, 0, 0.4)"
          />
        </view>

        <uni-search-bar
          v-model="keyword"
          :radius="8"
          :placeholder="placeholder"
          clear-button="no"
          class="search-input"
          cancel-button="none"
          bg-color="#ffffff"
        >
        </uni-search-bar>
        <view v-if="hasSeachIcon" class="icon-hot search-btn" @click="doSearch">
          <IconFont :size="14" :icon="icon" />
        </view>
      </view>
    </GradientBorder>
    <slot name="right"></slot>
  </view>
</template>
<script>
import isLogin from '@/common/mixins/isLogin';
import UniSearchBar from '@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue'

export default {
  name: 'MySearchBar',
  components:{UniSearchBar},
  props: {
    placeholder: {
      type: String,
      default: '请输入搜索内容',
    },
    type: {
      type: String,
      default: 'orange', // pink blue gray
    },
    value: {
      type: String,
      default: '',
    },
    hasSeachIcon: {
      type: Boolean,
      default: true,
    },
    hasLogo: {
      type: Boolean,
      default: false,
    },
    hasYx: {
      // 所有游戏标志
      type: Boolean,
      default: false,
    },
    yxName: {
      // 游戏的名字
      type: String,
      default: '所有游戏',
    },
    size: {
      type: String,
      default: '',
    },
    icon: {
      type: String,
      default: 'search',
    },
  },
  data() {
    return {
      keyword: '',
      productCategory: { label: '所有游戏', id: '' },
      gameData: [],
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        this.keyword = v;
      },
    },
    keyword(v) {
      this.$emit('input', v);
      this.$emit('change', v);
    },
  },
  mounted() {
    // if (this.hasYx) {
    //   getGameList().then((res) => {
    //     if (res.code == 200) {
    //       this.gameData = [{ label: '所有游戏', id: '' }].concat(
    //         res.data.map((item) => ({
    //           value: item.id,
    //           label: item.name,
    //         })),
    //       );
    //     }
    //   });
    // }
  },
  methods: {
    handelChooseGame() {
      this.$emit('chooseGame');
    },
    doCheck(item) {
      this.productCategory = item;
    },
    handelSearcBoxClick() {
      this.$emit('click', this.keyword, this.productCategory);
    },
    doSearch() {
      this.$emit('search', this.keyword, this.productCategory);
    },
    loginWrap() {
      if (!isLogin()) {
        uni.navigateTo({
          url: '/pages/login/login?isBack=1',
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .uni-searchbar__box {
  height: 60rpx !important;
}
.select-name {
  color: #1B1B1B;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
  margin-right: 8rpx;
}
.search-border {
  background:var(--linear-primary);
  width: 0;
  flex: 1;
}
.search-box {
  display: flex;
  align-items: center;
  background: #fff;
  // border: solid 4rpx $uni-color-primary;
  overflow: hidden;
  padding: 0 30rpx;
  box-sizing: border-box;

  /deep/.uni-searchbar__text-placeholder,
  .uni-input-placeholder {
    @include nomalEllipsis();
  }

  .select-list {
    color: #1B1B1B;
    margin-right: 20rpx;
    font-size: 24rpx;
    font-weight: 400;
    white-space: nowrap;
  }

  .search-btn {
    // color: $uni-color-primary;
    color: rgba(0, 0, 0, 1);
    flex-shrink: 0;
    text-align: right;
  }

  .search-input {
    padding: 0;
    flex: 1;
    margin-right: 10rpx;
    overflow: hidden;
    width: 0;

    /deep/ .uni-searchbar__box-icon-search {
      display: none;
    }

    /deep/ .uni-searchbar__box {
      padding: 0;
      height: 80rpx;
    }

    /deep/ .uni-searchbar__text-placeholder {
      color: $uni-text-color-placeholder;
      font-size: 24rpx;
      text-align: left;
      font-weight: 400;
    }

    /deep/ .uni-input-placeholder,
    .input-placeholder {
      color: $uni-text-color-placeholder;
      font-size: 24rpx;
    }
  }
}

.blue {
  .search-border {
    background: var(--linear-blue)
  }
  // .search-btn {
  //   color: $type-color-blue;
  // }
}

.pink {
  .search-border {
    background: var(--linear-pink)
  }
  // .search-btn {
  //   color: $type-color-pink;
  // }
}

.gray {
  .search-border {
    background: rgba(0, 0, 0, 0.1);
  }
  // .search-btn {
  //   color: rgba(0, 0, 0,1);
  // }
}
</style>
