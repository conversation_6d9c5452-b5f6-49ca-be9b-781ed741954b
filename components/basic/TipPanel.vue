<template>
  <view class="mask_body" @tap.stop>
    <view class="tip-panel">
      <view class="content">
        <view class="spaceBetween pre">
          <image
            src="../../assets/imgs/logo-text2.svg"
            mode="heightFix"
            style="height: 44rpx"
          />
          <view class="spaceStart">
            <text class="title"
              ><span class="gradient-main-primary">{{ subTitle }}</span>
            </text>

            <view class="icon-hot" style="margin-left: 34rpx;" @click="handelCancel"
              ><IconFont
                v-if="hasClose"
                :size="16"
                icon="close"
                style="color: black"
            /></view>
          </view>
        </view>
        <view
          v-if="title"
          class="title"
          style="margin-top: 40rpx; text-align: left"
          ><span class="gradient-main-primary">{{ title }}</span>
        </view>
        <view class="pre">
          <slot>
            <view class="text">{{ content }}</view>
          </slot>
        </view>

        <view class="spaceCenter pre">
          <view
            v-if="hasCancel"
            class="kk-btn line"
            style="min-width: 220rpx; margin-right: 26rpx"
            @click="handelCancel"
          >
            <span>{{ cancelText }}</span>
          </view>
          <view
            v-if="hasConfirm"
            :style="confirmBtnStyle"
            style="min-width: 220rpx"
            class="kk-btn primary"
            @click="handelConfirm"
            >{{ confirmText }}</view
          >
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    hasClose: {
      type: Boolean,
      default: true,
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    confirmText: {
      type: String,
      default: '确认',
    },
    subTitle: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    hasCancel: {
      type: Boolean,
      default: true,
    },
    hasConfirm: {
      type: Boolean,
      default: true,
    },
    content: {
      type: String,
      default: '',
    },
    confirmBtnStyle: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {
    handelConfirm() {
      this.$emit('confirm');
    },
    handelCancel() {
      this.$emit('cancel');
    },
  },
};
</script>
<style lang="scss" scoped>
.mask_body {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9900;
  background-color: rgba(0, 0, 0, 0.85);
}
.border-box {
  padding: 6rpx;
  background: black;
  width: fit-content;
  height: fit-content;
}
.tip-panel {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 40rpx;
  box-sizing: border-box;

  border-radius: 48rpx;
  overflow: hidden;

  padding: 6rpx; // 边框粗细
  @include mainColor(); // 边框颜色
}

.content {
  position: relative;
  background: #fffcf9;
  padding: 30rpx 34rpx;
  border-radius: 48rpx;

  &::before {
    content: '';
    background: url('../../assets/imgs/logo_bg.png') center no-repeat;
    width: 260rpx;
    height: 270rpx;

    position: absolute;
    right: 74rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 0;
  }

  font-size: 24rpx;
  font-weight: 400;
  color: #969696;

  .title {
    font-family: YouSheBiaoTiHei;
    font-size: 40rpx;
  }
  .text {
    margin-top: 16rpx;
  }
}
</style>
