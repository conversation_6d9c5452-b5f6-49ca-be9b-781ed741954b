<template>
  <uni-forms
    ref="customForm"
    :model-value="customFormData"
    :label-width="labelWidth * 2 + 'rpx'"
    :label-position="labelPosition"
    :class="labelPosition === 'top' ? 'top ' + theme : theme"
  >
    <view v-for="dataItem in dataJson" :key="dataItem.name">
      <uni-forms-item
        :name="dataItem.name"
        :rules="dataItem.rules"
        :label="dataItem.label"
        :required="dataItem.label && dataItem.required"
        :class="'formitem-' + dataItem.labelPosition"
      >
        <slot :name="dataItem.name + '-item'">
          <template slot="label">
            <slot :name="'label-' + dataItem.name"></slot>
          </template>

          <!-- 输入框 -->
          <uni-easyinput
            v-if="dataItem.type === 'input'"
            v-model="customFormData[dataItem.name]"
            :placeholder="dataItem.placeholder"
            v-bind="dataItem.itemProps"
            @input="(v) => onChange(v, dataItem)"
            @clear="(v) => onChange(v, dataItem)"
          >
            <view v-if="dataItem.prefixIcon" slot="left">
              <IconFont
                :size="18"
                :icon="dataItem.prefixIcon"
                class="input_icon"
              />
            </view>

            <template v-if="dataItem.hasCode" slot="right">
              <text class="c-primary" @click="sendCode(dataItem.phoneField)">{{
                codeMsg
              }}</text>
            </template>
          </uni-easyinput>

          <!-- 复选框 -->
          <uni-data-checkbox
            v-else-if="dataItem.type === 'checkbox'"
            v-model="customFormData[dataItem.name]"
            v-bind="dataItem.itemProps"
          />

          <!-- 选择框 -->
          <uni-data-picker
            v-else-if="dataItem.type === 'picker'"
            v-model="customFormData[dataItem.name]"
            :popup-title="dataItem.placeholder"
            v-bind="dataItem.itemProps"
            @change="onPickerChange(dataItem.name)"
            @nodeclick="onPickerNodeclick(dataItem.name)"
          ></uni-data-picker>

          <!-- 多图片上传 -->
          <upLoadList
            v-else-if="dataItem.type === 'upload'"
            :url-pic="customFormData[dataItem.name]"
            :name-key="dataItem.name"
            v-bind="dataItem.itemProps"
            @upSuccsessList="onUploadSuccess"
            @deletPicList="onUploadDel"
          />
          <!-- 单图上传 -->
          <upLoadSingle
            v-else-if="dataItem.type === 'uploadSingle'"
            :url-pic="customFormData[dataItem.name]"
            :name-key="dataItem.name"
            v-bind="dataItem.itemProps"
            @upSuccsessSingle="onUploadSingleSuccess"
          />

          <!-- 常规值 -->
          <view v-if="dataItem.type === 'text'">{{
            customFormData[dataItem.name]
          }}</view>
        </slot>
      </uni-forms-item>
      <slot :name="dataItem.name" />
    </view>
    <slot name="agreement"></slot>
    <slot></slot>
    <view v-if="hasSubmitBtn" :class="btnFixed ? 'fixedBottom' : ''">
      <button
        :class="btnDisabled ? 'disabled' : 'primary'"
        type="primary"
        class="kk-btn"
        style="width: 344rpx"
        @click="submit"
      >
        {{ submitText }}
      </button>
    </view>
  </uni-forms>
</template>
<script>
import upLoadList from '@/components/imgUpload/upLoadList.vue';
import upLoadSingle from '@/components/imgUpload/upLoadSingle.vue';

export default {
  components: {
    upLoadList,
    upLoadSingle,
  },
  props: {
    labelPosition: {
      type: String,
      default: 'left',
    },
    dataJson: {
      type: Array,
      default: () => [],
    },
    labelWidth: {
      type: Number,
      default: 0,
    },
    rules: {
      type: Object,
      default: () => {},
    },
    submitText: {
      type: String,
      default: '提交',
    },
    hasSubmitBtn: {
      type: Boolean,
      default: true,
    },
    theme: {
      type: String,
      default: 'default', // thin
    },
    defaultFormData: {
      type: Object,
      default: null,
    },
    btnFixed: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      customFormData: {},

      isDis: false,
      codeMsg: '获取验证码',
      time: 60,

      btnDisabled: true,
    };
  },
  watch: {
    customFormData: {
      deep: true,
      handler(v) {
        this.handelFieldChange();
      },
    },
  },
  created() {
    this.requiredFileds = this.dataJson
      .filter((e) => e.required)
      .map((e) => e.name);

    if (this.defaultFormData) {
      this.customFormData = JSON.parse(JSON.stringify(this.defaultFormData));
    }
  },
  methods: {
    onChange(v, fieldItem) {
      this.$emit('change', fieldItem, v);
    },
    // 单张上传-组件上传成功
    onUploadSingleSuccess(url, filed) {
      this.customFormData[filed] = url;
    },
    onPickerChange(field) {},
    onPickerNodeclick(field) {},
    onUploadSuccess(url, filed) {
      const data = this.customFormData[filed] || [];
      data.push(url);
      this.$set(this.customFormData, filed, data);
    },
    onUploadDel(index, filed) {
      this.customFormData[filed].splice(index, 1);
    },
    /**
     * 获取验证码
     */
    sendCode(field) {
      const telephone = this.customFormData[field];
      if (this.isDis == true) {
        uni.showToast({
          title: '请稍后重试',
          icon: 'none',
        });
        return;
      }
      this.$emit('sendCode', telephone, this.countDown);
    },
    /**
     * 倒计时
     */
    countDown() {
      this.time -= 1;
      if (this.time == 0) {
        this.time = 60;
        this.codeMsg = '获取验证码';
        this.isDis = false;
        clearInterval(interval);
        return;
      }
      this.codeMsg = '重新获取' + this.time + 'S';
      this.isDis = true;
      var _this = this;
      var interval = setTimeout(function () {
        _this.countDown();
      }, 1000);
    },
    // 必填项是否都填写
    handelFieldChange() {
      console.log(
        'handelFieldChange',
        this.customFormData,
        this.requiredFileds,
      );
      const hasEmpty = this.requiredFileds.some((field) => {
        const data = this.customFormData[field];
        return !data && data !== 0;
      });
      this.btnDisabled = hasEmpty;
    },
    submit(callback) {
      this.$refs['customForm']
        .validate()
        .then((res) => {
          // console.log('success', res);
          // uni.showToast({
          //   title: `校验通过`,
          // });
          if (typeof callback === 'function') {
            callback(this.customFormData);
          }
          this.$emit('submit', this.customFormData);
        })
        .catch((err) => {
          console.log('err', err);
        });
    },
    // 外部使用
    setFieldValue(field, value) {
      this.$set(this.customFormData, field, value);
    },
  },
};
</script>
<style lang="scss" scoped>
$border-color: #969696;
$border-raduis: 40rpx;
$pd-x: 28rpx;

$icon-color: #969696;
$label-color: #222;
$text-color: #333;
$placeholder-color: #c0c0c0;

.thin {
  $border-color: rgba(0, 0, 0, 0.1);

  /deep/ .input_box,
  /deep/ .input-value-border,
  /deep/ .checklist-box,
  /deep/ .is-input-border {
    border-color: $border-color !important;
  }

  /deep/ .uni-forms-item {
    margin-bottom: 26rpx;
  }
  /deep/ .uni-forms-item__label {
    color: #1b1b1b;
    font-family: 'PingFang SC';
    font-size: 28rpx;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1.2rpx;
  }
}

.top {
  /deep/ .uni-forms-item__label {
    justify-content: flex-start !important;
    width: 100% !important;
  }
}
.uni-forms-item {
  // align-items: center;
}

//uni-forms-item

/deep/ .uni-forms-item__error {
  padding-left: 34rpx;
  color: $uni-color-primary;
}

/deep/ .uni-forms-item .is-required {
  color: $uni-color-primary;
}

/deep/ .uni-forms-item__label {
  color: $label-color;
  font-size: 24rpx;
  font-weight: 400;
  justify-content: flex-end !important;
}

// input
/deep/ .is-input-border {
  border-radius: 40rpx;

  padding: 0 $pd-x;
  box-sizing: border-box;
  border-color: $border-color !important;

  &.is-disabled {
    background-color: #fff6eb !important;

    .uni-input-input:read-only {
      color: #1b1b1b !important;
    }
  }
}
/deep/ .is-input-error-border {
  color: $text-color !important;
  border-color: $uni-color-primary !important;

  .content-clear-icon {
    color: $uni-color-primary !important;
  }
}

/deep/ .uni-easyinput {
  color: $text-color;
  font-size: 28rpx;

  .input-padding[data-v-abe12412] {
    padding-left: 0 !important;
  }
}

/deep/ .uni-easyinput__content-input {
  height: 68rpx;
  font-size: 28rpx;
  color: $text-color;
}
/deep/ .uni-easyinput__content-textarea {
  font-size: 28rpx;
}

// placeholder
/deep/ .uni-easyinput__placeholder-class {
  color: $placeholder-color;
  font-size: 24rpx;
  font-weight: 400;
}

/deep/ .is-input-error-border .uni-easyinput__placeholder-class {
  color: $placeholder-color;
}

/deep/ .content-clear-icon {
  padding-right: 0;
}

.input_box {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  margin-bottom: 40rpx;

  border-radius: $border-raduis;
  border: 1rpx solid $border-color;

  font-size: 28rpx;
  color: $text-color;
  font-weight: 500;
}

/deep/ .input_icon {
  margin-right: 0 !important;
  color: $icon-color;

  width: 44rpx;
  height: 44rpx;
  margin-right: 30rpx;
}
input::focus {
  border: none;
}

// picker
/deep/ .input-value-border {
  border-radius: $border-raduis;
  padding: 0 $pd-x;
  box-sizing: border-box;
  border-color: $border-color;
  height: 70rpx;
  font-size: 28rpx;

  .uniui-clear {
    color: $icon-color !important;
  }

  .placeholder {
    color: $placeholder-color;
    font-size: 24rpx;
  }
}

/deep/ .checklist-group {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: 24rpx;
  row-gap: 24rpx;

  .checklist-box {
    display: flex;
    padding: 16rpx 0;
    justify-content: center;
    align-items: center;
    text-align: center;

    border-radius: 40rpx;
    border: 2rpx solid $border-color;
    background: #fff;

    margin-right: 0rpx !important;
    margin-bottom: 0rpx !important;
    margin-top: 0rpx !important;

    .checklist-text {
      color: rgba(0, 0, 0, 0.4) !important;
      font-family: 'PingFang SC';
      font-size: 24rpx !important;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.96rpx;

      margin-left: 0rpx !important;
      width: 100%;
    }

    &.is-checked {
      border: 1rpx solid var(--logo-color, #ff7a00);
      background: var(--Dark-Orange, #ff720c);

      .checklist-text {
        color: #fff !important;
      }
    }

    .radio__inner {
      display: none !important;
    }
  }
}

.uni-forms-item.formitem-top {
  display: flex;
  flex-direction: column !important;

  /deep/ .uni-forms-item__label {
    justify-content: flex-start !important;
  }
}
</style>
