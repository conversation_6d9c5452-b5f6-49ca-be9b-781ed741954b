<template>
  <view class="status-btns">
    <scroll-view  :scroll-left="scrollLeft" class="content-scroll" scroll-x style="white-space: nowrap;">
      <text
        v-for="(item,index) in newListData"
        :key="item.value"
        :class="status === item.value ? 'active' : ''"
        class="scroll-item"
        style="margin-right: 56rpx"
        @click="changeStatus(item.value, item,index)"
        >{{ item.label }}</text
      >
    </scroll-view>
  </view>
</template>
<script>
export default {
  props: {
    hasAll: {
      type: Boolean,
      default: true,
    },
    listData: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [Number, String],
      default: '',
    },
  },
  data() {
    return {
      status: '',

      scrollLeft:0,
      contentScrollW:0,
      category:[]
    };
  },
  computed:{
    newListData(){
      let data = this.listData
      if(this.hasAll){
        data = [{label:'查看全部',value:''}].concat(data)
      }
      return data
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        this.status = v;
      },
    },
    status(v){
      this.$emit('input',v)
    }
  },
  mounted(){
    this.getScrollW()
  },
  methods: {
    getScrollW(){
     const query = uni.createSelectorQuery().in(this)
     query.select('.content-scroll').boundingClientRect(data=>{
      this.contentScrollW = data.width
     }).exec()

     query.selectAll('.scroll-item').boundingClientRect(data=>{
      this.category =  data.map((ele) => {
        return{
          left:ele.left,
          width:ele.width
        }
      });
     }).exec()

    },
    changeStatus(value, item,index) {
      if(this.category[index]){
        const{left,width} = this.category[index]
        this.scrollLeft = left-this.contentScrollW/2+width/2
      }
      this.status = value;
      this.$emit('change', value, item);
    },
  },
};
</script>
<style lang="scss" scoped>
.status-btns {
  background: #fff;
  color: rgba(0, 0, 0, 0.4);
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.48px;

  padding: 0 42rpx;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
  text{
    line-height: 104rpx;
  }
  .active {
    color: #1b1b1b;
    font-style: normal;
    font-weight: 500;
    letter-spacing: 0.48px;
  }
}
</style>
