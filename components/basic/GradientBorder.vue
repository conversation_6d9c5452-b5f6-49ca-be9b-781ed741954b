<template>
  <view :style="contentStyle" class="gradient-border">
    <view :style="contentStyle" class="content">
      <slot />
    </view>
  </view>
</template>
<script>
export default {
  name: 'GradientBorder',
  props: {
    noTopLeft: {
      type: Boolean,
      default: false,
    },
    noTopRight: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
      default: '',
    },
    round: {
      type: Number,
      default: 12,
    },
    borderSize: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      contentStyle: '',
      activeIndex: 0,
    };
  },
  watch: {
    noTopLeft(v) {
      this.setStyle();
    },
    noTopRight(v) {
     this.setStyle();
    },
  },
  created(){
    this.setStyle()
  },
  methods: {
    setStyle() {
      let str = `border-radius:${this.round}px;`;
      str += `border-top-left-radius:${this.noTopLeft?0:this.round}px;`;
      str += `border-top-right-radius:${this.noTopRight?0:this.round}px;`;
      str += `padding:${this.borderSize}px;`;
      this.contentStyle = str
    },
  }
};
</script>

<style lang="scss" scoped>
$border-color: rgba(255, 225, 195, 0.8);
.gradient-border {
  position: relative;
  box-sizing: border-box;

  background: var(--linear-border);
  // backdrop-filter: blur(50px);
  padding: 1px;
  height: auto;

  .content {
    position: relative;
    background: #fff;
    top: 0;
    overflow: hidden;
  }
}
</style>
