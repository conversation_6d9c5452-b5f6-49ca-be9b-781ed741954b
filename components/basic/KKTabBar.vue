<template>
  <m-tabbar ref="tabbar" native>
    <template v-slot:tabbar_index_2>
      <view class="m-tabbar__item" @click="midFun">
        <image
          src="../../static/imgs/tabBar/sale.png"
          class="m-tabbar__icon_img"
          mode="heightFix"
          style="height: 156rpx; display: block; margin-top: -54rpx"
        />
        <view
          class="m-tabbar__label main__label"
          style="margin-top: -50rpx;"
          >卖号</view
        >
      </view>
    </template>

    <template v-slot:tabbar_index_3 v-if="hasNews">
        <view class="m-tabbar__item" @click="fakeClick">
          <image
            src="@/static/imgs/tabBar/msg_light.png"
            class="m-tabbar__icon_img"
            mode="widthFix"
            style="width: 50rpx; display: block;"
          />
          <view
            class="m-tabbar__label"
            style="
              font-size: 24rpx;
              padding-top: 4rpx;
            "
            >消息</view
          >
        </view>
      </template>
  </m-tabbar>
</template>

<script>
export default {
  props:{
    hasNews:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {};
  },
  methods: {
    midFun() {
      uni.navigateTo({
        url: '/pages/sale/sale',
      });
    },
    setTabBarBadge(data){
      this.$refs.tabbar.setTabBarBadge(data);
    }
  },
};
</script>
<style lang="scss" scoped>
.main__label{
  font-size: 28rpx; 
  color: rgb(153, 153, 153);
  margin-top: -34rpx ;
  position:relative;
  z-index:999;

  color: $uni-color-primary;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
}

.m-tabbar__label{
  color: $uni-color-primary;
}
/deep/ .m-tabbar__icon .m-tabbar__badge{
  border-radius: 48rpx;
  background: linear-gradient(87deg, #FF002E 3.31%, #FFC0C0 142.11%);
  box-shadow: 0px 0px 5px 0px rgba(255, 255, 255, 0.60) inset;

  display: flex;
  height: 36rpx;
  line-height: 36rpx;
  min-width: 36rpx;
  width: fit-content;
  box-sizing: border-box;

  padding: 0 14rpx;

  color: #FFF;
  text-align: center;
  font-family: Inter;
  font-size: 20rpx;
  font-style: normal;
  font-weight: 700;
  letter-spacing: 0.2px;
}
</style>