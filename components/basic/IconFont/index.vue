<template>
  <!-- <svg :style="sizeStyle" class="iconfont" aria-hidden="true" @click="$emit('click')">
    <use :xlink:href="href"></use>
  </svg> -->
  <text :class="href" :style="sizeStyle" class="iconfont" @click="$emit('click')" ></text>
</template>

<script >

import { watch } from '@vue/composition-api';

export default {
  name: 'IconFont',
  components: {},
  props: {
    icon: {
      type:String,
      default:''
    },
    size: {
      type:Number,
      default:14
    },
    color:{
      type:String,
      default:''
    }
  },
  data(){
    return{
      sizeStyle:{}
    }
  },
  computed: {
    href () {
      // return '#' + 'kk-icon-' + this.icon
      return 'kk-icon-' + this.icon

    }
  },
  watch:{
    size:{
      immediate: true,
      handler(v) {
        this.sizeStyle = this.getSizeStyle()
      },
    }
  },
  mounted(){
  },
  methods:{
     getSizeStyle () {
      let styles =  {
        // width: this.size + 'px',
        // height: this.size + 'px',
        fontSize:this.size + 'px'
      }
      if(this.color){
        styles.color=this.color
      }
      return styles
    }
  },
}
</script>

<style lang="less" scoped>
@import url('./iconfont.css');

.iconfont {
  display: inline;
  width: 1em;
  height: 1.05em;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
  margin-top: -2px;
}
</style>
