<template>
  <view class="select-box-panel" @touchmove.stop>
    <view class="spaceStart">

      <!-- 父级 -->
      <scroll-view
        v-if="hasParent"
        :style="{
          height: scrollH ? scrollH + 'rpx' : 'auto',
          borderRight: '1px solid #f3f3f3',
          width:'fit-content'
        }"
        scroll-y
        class="select-box-list"
      >
        <view v-for="(item, index) in parentOptions" :key="index">
          <view
            :class="item.value == currentParentValue ? 'active' : ''"
            class="select-item-box"
            @click="handelParentClick(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </scroll-view>

      <!-- 子级 -->
      <scroll-view
        :style="{ height: scrollH ? scrollH + 'rpx' : 'auto',flex:1 }"
        scroll-y
        class="select-box-list"
      >
        <view v-for="(item, index) in options" :key="index">
          <view
            :class="item.value == currentValue ? 'active' : ''"
            class="select-item-box"
            @click="doClick(item)"
          >
            {{ item.label }}
            <IconFont v-if="item.value == currentValue" :size="16" icon="check" />
          </view>
        </view>
      </scroll-view>
    </view>
    <view v-if="hasBtns" class="btns_box spaceAround">
      <view class="kk-btn line" style="width: 45%" @click="reset"
        ><span>重置</span>
      </view>
      <view class="kk-btn primary" style="width: 45%" @click="confirm"
        >确认</view
      >
    </view>
  </view>
</template>
<script>
const HEAD_H = 48;
export default {
  name: 'Select',
  props: {
    parentOptions:{ //支持二级选择
      type: Array,
      default: () => [],
    },
    dataList: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [Number, String],
      default: '',
    },
    hasBtns: {
      type: Boolean,
      default: true,
    },
    scrollH: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      id: 'select-box-' + '11',
      popupShow: false,

      currentValue: '',
      currentItem: '',
      selectboxStyle: '',

      currentParentValue:''
    };
  },
  computed:{
    hasParent(){
      return this.parentOptions && this.parentOptions.length
    },
    options(){
      if(this.hasParent){
        const obj =this.parentOptions.filter(e=>e.value === this.currentParentValue)[0]||{}
        return obj.childList||[]
      }
      return this.dataList
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.currentValue = newVal;

        if(this.hasParent){ 
          const [parent,child] = (newVal||'').split('|')
          this.currentParentValue = parent
          this.currentValue = child;
        }
      },
    },
  },
  mounted() {
 
  },
  methods: {
    handelParentClick(v){ // 父级点击
      if(this.currentParentValue!==v){
        this.currentParentValue = v
        this.currentValue = ''
        this.currentItem = {}
      }
    },
    doClick(item) { // 子级点击
      if(this.currentValue === item.value && this.hasParent){
        this.currentValue = ''
        this.currentItem = {}
      }else{
        this.currentValue = item.value;
        this.currentItem = item;
      }

      if (!this.hasBtns) {
       this.confirm()
      }
    },
    reset() {
      this.currentParentValue = ''

      this.currentValue = '';
      this.currentItem = null;
      this.$emit('reset');
      this.$emit('change', '', null);
    },
    confirm() {
      let value = this.currentValue

      if(this.hasParent){
        value = [this.currentParentValue]
        if(this.currentValue){
         value.push(this.currentValue)
        }
        value = value.join('|')
      }

      this.$emit('confirm', value, this.currentItem);
      this.$emit('change', value, this.currentItem);
    },
  },
};
</script>

<style lang="scss" scoped>
.select-box {
  &.active {
    .select-name {
      svg {
        transform: rotate(180deg);
      }
    }
  }
}
.select-name {
  color: #222;
  font-size: 24rpx;
  font-weight: 400;
  white-space: nowrap;
}
.select-box-panel {
  position: relative;
  // padding: 32rpx 22rpx;
  background-color: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);

  color: rgba(0, 0, 0, 0.4);
  font-size: 24rpx;
  font-weight: 400;
  box-sizing: border-box;

  .select-box-list {
    padding: 32rpx 22rpx;
    box-sizing: border-box;
  }

  .select-item-box {
    align-items: center;
    display: flex;
    justify-content: space-between;
    padding: 16rpx 20rpx;
    line-height: 34rpx;
    margin-bottom: 8rpx;

    color: rgba(0, 0, 0, 0.4);
    font-size: 24rpx;
    font-weight: 400;

    &.active {
      color: $uni-color-subtitle;
      background: #fdf5ed;
      font-weight: 500;
    }
  }
}

.btns_box {
  border-top: 1rpx solid #f3f3f3;
  background: #fff;
  box-shadow: -1px -2px 3px 0px rgba(0, 0, 0, 0.05);
  padding: 16rpx 40rpx;
}
</style>
