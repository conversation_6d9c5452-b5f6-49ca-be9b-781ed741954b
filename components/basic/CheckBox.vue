<template>
  <view class="spaceStart">
    <view
      v-for="(ele, idx) in inputList"
      :key="idx"
      :class="ele.value === value ? 'active' : ''"
      class="select-item"
      @click="handelClick(ele.value)"
    >
      {{ ele.label }}
    </view>
  </view>
</template>
<script>
export default {
  props: {
    dataList: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      inputList:[]
    };
  },
  mounted(){
    console.log(typeof this.dataList[0])
    if(this.dataList && typeof this.dataList[0] !=='object'){
      this.inputList = this.dataList.map(i=>({value:i,label:i}))
    }else{
      this.inputList = this.dataList
    }
  },
  methods: {
    handelClick(value) {
      this.$emit('input', value);
      if(this.value===value){
        this.$emit('click', '');
      }else{
        this.$emit('click', value);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.select-item {
  flex: 1;
  margin-right: 20rpx;
  padding: 0 14rpx;

  background-color: #fff;
  height: 70rpx;
  line-height: 30rpx;

  box-sizing: border-box;
  border-radius: 40rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);

  color: rgba(0, 0, 0, 0.4);
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.48px;

  display: flex;
  align-items: center;
  justify-content: center;

  &:last-child {
    margin-right: 0;
  }

  &.active {
    border: 1px solid #ff7a00;
    background: #ff720c;
    color: #fff;
  }
}
</style>
