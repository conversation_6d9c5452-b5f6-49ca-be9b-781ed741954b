<template>
  <view class="card-box">
    <view class="spaceBetween title-box" @click="doClick">
      <view class="title"
        >{{ title }}
        <slot name="title"></slot>
      </view>

      <IconFont
        :size="16"
        :class="isHide ? '' : 'rotate-180'"
        icon="arrow-d"
      />
    </view>

    <view v-if="!isHide" :class="isHide ? 'hide' : ''" class="card-content">
      <slot></slot>
    </view>
  </view>
</template>
<script>
export default {
  name: 'Card',
  props: {
    hide: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '请输入搜索内容',
    },
    type: {
      type: String,
      default: 'orange', // pink blue
    },
  },
  data() {
    return {
      isHide: false,
    };
  },
  watch: {},
  mounted() {
    this.isHide = this.hide;
  },
  methods: {
    doClick() {
      this.isHide = !this.isHide;
    },
  },
};
</script>

<style lang="scss" scoped>
.card-box {
  border-bottom: solid 1rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx 0;

  // transition: all 3s ease;

  .title-box {
    color: $uni-color-subtitle;
    font-size: 28rpx;
    font-weight: 500;
    height: fit-content;

    .title{
      flex:1
    }

    .rotate-180 {
      transform: rotate(180deg);
    }
  }

  .card-content {
    margin-top: 24rpx;

    // transition: height 2s linear;

    &.hide {
      margin-top: 0;
      height: 0;
      // visibility: hidden;
      display: none;
    }
  }
}
</style>
