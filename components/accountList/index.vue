<template>
  <view>
    <!-- <view class="totalNum">当前商品数:{{ total }}</view> -->
    <view
      v-if="bannerList && bannerList.length"
      style="position: relative; margin-bottom: 10rpx"
    >
      <swiper
        :autoplay="autoplay"
        :interval="interval"
        :duration="duration"
        class="swiper_box"
        indicator-dots
      >
        <swiper-item
          v-for="(item, index) in bannerList"
          :key="index"
          @click="pageBannerGo(item)"
        >
          <image :src="item.pic" class="banner_pic" mode="widthFix"></image>
        </swiper-item>
      </swiper>

      <!-- <image
        src="../../assets/imgs/g_btn_click.svg"
        mode="widthFix"
        class="btn-icon"
      /> -->
      <!-- <view class="kk-btn primary btn-icon">点击进入</view> -->
    </view>

    <Goods
      :account-shop-list="accountShopDate"
      :is-big="isShowBig"
      :is-loading="isLoading"
      :goods-type="goodsType"
      has-border
      @click="goAccountDetail"
    />
  </view>
</template>

<script>
import mpHtml from '../../uni_modules/mp-html/components/mp-html/mp-html.vue';
import Goods from '@/components/accountList/goods.vue';

export default {
  components: { mpHtml, Goods },
  props: {
    total: {
      type: Number,
      default: 0,
    },
    isShowBig: {
      type: Boolean,
      default: false,
    },
    accountShopDate: {
      type: Array,
      default() {
        return [];
      },
    },
    bannerList: {
      type: Array,
      default() {
        return [];
      },
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    goodsType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      autoplay: true,
      circular: true,
      duration: 500,
      interval: 3500,
    };
  },
  methods: {
    goAccountDetail({ id ,productCategoryId}) {
      uni.navigateTo({
          url: '/pages/accountDetail/accountDetail?productId=' + id,
        });
      // if(this.goodsType==='vgoods'){
      //   uni.navigateTo({
      //     url: `/pages/confirmOrder/confirmOrder?productId=${id}&productCategoryId=${productCategoryId}`,
      //   });
      // }else{
      //   uni.navigateTo({
      //     url: '/pages/accountDetail/accountDetail?productId=' + id,
      //   });
      // }
    },
    pageBannerGo(date) {
      const { h5url = '', appurl = '' } = date;
      // #ifdef H5
      if (h5url.indexOf('http') == 0) {
        window.open(h5url);
      } else {
        uni.navigateTo({
          url: h5url,
        });
      }
      // #endif
      // #ifdef APP-PLUS
      if (appurl.indexOf('http') == 0) {
        plus.runtime.openURL(appurl);
      } else {
        uni.navigateTo({
          url: appurl,
        });
      }
      // #endif
    },
  },
};
</script>

<style lang="scss">
/deep/ uni-swiper .uni-swiper-dots-horizontal {
  right: 20rpx;
  left: auto;
}

/deep/ .uni-swiper-dot {
  background: #f5f5f5;
  width: 4rpx;
  height: 4rpx;

  &.uni-swiper-dot-active {
    background: #f7a95b;
  }
}

.swiper_box {
  height: 67px;
  width: 100%;
}
.banner_pic {
  border-radius: 10px;
  width: 100%;
}
.btn-icon {
  width: 128rpx;
  font-size: 16rpx;
  height: 38rpx;
  line-height: 38rpx;
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  pointer-events: none;
}
</style>
