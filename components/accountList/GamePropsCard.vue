<template>
  <view class="game-props-card">
    <view class="spaceStart cardBox">
      <view class="item_title">
        <view v-if="tradeType" :class="colorMap[tradeType]" class="tag">{{ tradeType }}</view>
        <text v-if="trading" style="margin-left: -2px;">【{{ trading }}】</text>
        <text v-if="productName" style="margin-left: -4px;">【{{productName}}】</text>
        {{ stockTip }}
      </view>
    </view>

    <view class="spaceStart" style="margin: 20rpx 0; color: #666">
      <view class="ext_item">{{ item.productSn }}</view>
      <text style="margin: 0 8rpx">|</text>
      <view class="ext_item">{{ item.gameAccountQufu }}</view>
      <text style="margin: 0 8rpx">|</text>
      <view class="store"
        >库存
        <text
          style="margin: 0 10rpx; color: rgb(51, 153, 255); font-weight: 700"
          >{{ stock }}</text
        >
      </view>
    </view>

    <view class="spaceBetween">
      <rich-text :nodes="priceTip"> </rich-text>
      <view class="item_price"
        ><text style="font-size: 24rpx">￥</text>{{ item.price }}</view
      >
    </view>
  </view>
</template>
<script>
const colorMap = {
  '卖家发货': 'green',
  '平台代发': 'blue',
};
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      colorMap,
    };
  },
  computed: {
    productName(){
      return this.getAttrText('商品名称');
    },
    tradeType() {
      return this.getAttrText('交易类型');
    },
    trading() {
      return this.getAttrText('交易方式');
    },
    priceTip() {
      return this.getAttrText('比例说明').replace(/<br\/>/g, '  /')
        .split('')
        .map((char) =>
          isNaN(char) ? char : `<span style='color: #FF5C00'>${char}</span>`,
        )
        .join('');
    },
    stockTip() {
      const num = this.getAttrText('数量');
      const unit = this.getAttrText('单位');
      return `${num}${unit}=${this.item && this.item.price}元`;
    },
    stock() {
      return this.getAttrText('发布件数')
    },
  },
  methods:{
    getAttrText(key){
      if(this.item && this.item.attrValueList){
        const arr = this.item.attrValueList||[]
        return (arr.find((el) => el.name === key)||{}).value;
      }
      return ''
    }
  }
};
</script>

<style lang="scss" scoped>
.game-props-card {
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-weight: 400;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);

  padding: 12rpx 16rpx;

  margin-bottom: 16rpx;
  background: #fff;
  border-radius: 16rpx;

  .item_title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
  }

  .item_price {
    color: #ff720c;
    font-size: 32rpx;
    font-weight: 600;
  }

  .brand-txt {
    color: #ffb74a;
    position: relative;
  }

  .tag {
    color: #fff;
    border-radius: 8rpx;
    background: #30c6a6;
    padding: 2rpx 12rpx;
    display: inline-block;

    &.blue {
      background: #488cf6;
    }
  }
}
</style>
