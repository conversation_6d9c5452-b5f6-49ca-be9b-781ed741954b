<template>
  <view v-if="isAuthVisible" class="authContentBox">
    <view class="modal">
      <view class="title"> 权限说明 </view>
      <view class="content">
        <view v-if="!isLabel"> 看看账号网向您申请如下权限 </view>
        <view v-else >{{isLabel}}</view>
        <view v-if="!cancelText">
          <view> 相机权限：用于拍照、录制视频以及实名认证中的校验等场景。 </view>
        <view>
          存储权限：用于APP写入、保存、读取、修改、发布商品上传照片视频、提供投诉资料、聊天发送图片、分析保存图片、等场景的应用
        </view>
        <view> 允许后您可以通过手机系统设置对授权进行管理 </view>
        </view>
        <view v-else>
          {{cancelText}}
        </view>
      </view>
      <view class="spaceBetween">
        <view class="kk-btn line" @click="refuse" style="width: 48%"
          ><span>{{cancelBtnText}}</span>
        </view>

        <view class="kk-btn primary" @click="submit" style="width: 48%" 
          >{{submitText}}</view
        >
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    isAuthVisible:{
        type:Boolean,
        default:false
    },
    isLabel:{
      type:String,
      default:''
    },
    submitText:{
      type:String,
      default:'允许'
    },
    cancelBtnText:{
      type:String,
      default:'拒绝'
    },
    cancelText:{
      type:String,
      default:''
    }
    
  },
  data() {
    return {};
  },
  mounted() {
    
  },
  onHide() {},
  methods: {
    refuse(){
        this.$emit('authRefuse')
    },
    submit(){
        this.$emit('authSubmit')
    },
  },
};
</script>

<style scoped lang="scss">
.authContentBox {
  position: fixed;
  top: 0px;
  left: 0px;
  background: rgba(0, 0, 0, 0.2);
  width: 100vw;
  height: 100vh;
  z-index: 9999999;
  .modal {
    width: 80%;
    min-height: 300rpx;
    background: #fff;
    border-radius: 20rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    padding: 40rpx 30rpx;
    box-sizing: border-box;
    transform: translate(-50%, -50%);
    .title {
      text-align: center;
      font-size: 32rpx;
      color: #000;
    }
    .content {
      font-size: 28rpx;
      margin-top: 10rpx;
      color: #000;
      margin-bottom: 20rpx;
      line-height: 50rpx;
    }
  }
}
</style>
