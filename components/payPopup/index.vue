<template>
  <view>
    <uni-popup ref="popup" style="z-index: 200" type="bottom" @change="change">
      <view class="payWay_wrap">
        <view class="pay-popup-head"> 选择支付方式 </view>
        <view>
          <view v-if="type != 1" class="pop_payPrice">
            <span style="font-size: 28rpx">￥</span>{{ orderDetail.payAmount }}
          </view>
          <view v-if="type == 1" class="pop_payPrice">
            <span style="font-size: 28rpx">￥</span>200000
          </view>
          <radio-group class="payMethod" @change="changePay">
            <label v-if="IS_OPEN_ZFB_PAY" class="radio_gropuItem spaceBetween">
              <view class="spaceStart">
                <image
                  class="pay_pic"
                  src="../../static/al.png"
                  mode="widthFix"
                ></image>
                <view>支付宝支付</view>
              </view>
              <radio
                style="transform: scale(0.7)"
                value="2"
                checked
                color="#FC6116"
              />
            </label>
            <label class="radio_gropuItem spaceBetween">
              <view class="spaceStart">
                <image
                  class="pay_pic"
                  src="../../static/wx.png"
                  mode="widthFix"
                ></image>
                <view>微信支付</view>
              </view>
              <radio :checked="!IS_OPEN_ZFB_PAY" style="transform: scale(0.7)" value="1" color="#FC6116"/>
            </label>
          </radio-group>
        </view>

        <view
          class="kefu_btn"
          style="
            width: 100%;
            text-align: center;
            margin-top: 30rpx;
            padding: 26rpx 0;
          "
          @click="payNowFun"
          >立即购买</view
        >
      </view>
    </uni-popup>
  </view>
</template>

<script>
const IS_OPEN_ZFB_PAY = false // 是否开放支付宝支付

export default {
  props: {
    orderDetail: {
      type: Object,
      default: ()=>{},
    },
    type: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      IS_OPEN_ZFB_PAY,
      method: IS_OPEN_ZFB_PAY ? 2:1,// 1 微信支付 2 支付宝支付
    };
  },
  mounted() {
    this.showPopup();
  },
  onHide() {
    this.hidePopup();
  },
  methods: {
    change(e) {
      if (!e.show) {
        this.$emit('close');
      }
    },
    changePay(e) {
      this.method = e.detail.value;
    },
    payNowFun() {
      this.$emit('payNowFun', this.method, this.orderDetail.id);
    },
    showPopup() {
      this.$refs.popup.open('bottom');
    },
    hidePopup() {
      this.$refs.popup.close();
    },
  },
};
</script>

<style scoped>
.kefu_btn {
  background: #fc6116;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  padding: 18rpx 30rpx;
  box-sizing: border-box;
}
</style>
