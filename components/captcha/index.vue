<!-- components/slide-verify/index.vue -->
<template>
    <view class="verify-wrapper">
      <!-- 验证区域 -->
      <view class="verify-container" :style="{ backgroundImage: `url(${bgImage})` }">
        <view 
          class="verify-move" 
          :style="{ 
            top: `${moveTop}px`,
            left: `${sliderLeft}px`,
            backgroundImage: `url(${bgImage})`,
            backgroundPosition: `-${targetLeft}px -${moveTop}px`
          }"
        ></view>
        <view 
          class="verify-target" 
          :style="{ top: `${moveTop}px`, left: `${targetLeft}px` }"
        ></view>
        <text class="verify-reset" @tap="init">刷新</text>
      </view>
  
      <!-- 滑动控制条 -->
      <view class="slider-control">
        <text class="slider-info">{{ sliderText }}</text>
        <view 
          class="slider" 
          @touchstart="onTouchStart"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
          :style="{ left: `${sliderLeft}px` }"
        ></view>
        <view class="slider-shadow" :style="{ width: `${sliderLeft + 4}px` }"></view>
      </view>
    </view>
  </template>
  
  <script>
  export default {
    props: {
      bgImage: {
        type: String,
        default: require('@/components/captcha/cap.png') // 正确引入图片路径
      },
      faultSize: {
        type: Number,
        default: 10 // 容错值
      }
    },
    data() {
      return {
        moveTop: 180,      // 验证块初始top
        targetLeft: 200,   // 目标区域left
        sliderLeft: 0,     // 滑块当前位置
        maxDistance: 450,  // 最大滑动距离
        isDragging: false,
        startX: 0,
        sliderText: "按住左边按钮向右拖动完成验证"
      };
    },
    methods: {
      // 初始化验证位置
      init() {
        this.moveTop = Math.floor(Math.random() * 220);
        this.targetLeft = Math.floor(Math.random() * 210 + 160);
        this.sliderLeft = 0;
        this.sliderText = "按住左边按钮向右拖动完成验证";
      },
      
      // 触摸开始
      onTouchStart(e) {
        this.isDragging = true;
        this.startX = e.touches[0].clientX - this.sliderLeft;
      },
      
      // 触摸移动
      onTouchMove(e) {
        if (!this.isDragging) return;
        const moveX = e.touches[0].clientX - this.startX;
        this.sliderLeft = Math.max(0, Math.min(moveX, this.maxDistance));
      },
      
      // 触摸结束
      onTouchEnd() {
        if (!this.isDragging) return;
        this.isDragging = false;
        
        const isSuccess = 
          this.sliderLeft >= this.targetLeft - this.faultSize && 
          this.sliderLeft <= this.targetLeft + this.faultSize;
        
        this.$emit("verify", isSuccess);
        this.sliderText = isSuccess ? "验证成功" : "验证失败";
        setTimeout(this.init, 1500);
      }
    },
    mounted() {
      this.init();
    }
  };
  </script>
  
  <style scoped>
  .verify-wrapper {
    width: 500rpx;
    margin: 0 auto;
  }
  .verify-container {
    position: relative;
    width: 500rpx;
    height: 300rpx;
    border-radius: 16rpx;
    background-size: 100% 100%;
    overflow: hidden;
  }
  .verify-move {
    position: absolute;
    width: 50px;
    height: 50px;
    z-index: 3;
    background-size: 500rpx 300rpx;
  }
  .verify-target {
    position: absolute;
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.6);
    z-index: 2;
  }
  .verify-reset {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    color: #fff;
    z-index: 1;
  }
  .slider-control {
    position: relative;
    width: 500rpx;
    height: 50px;
    margin-top: 20rpx;
    background: #f2f2f2;
    border-radius: 8rpx;
    overflow: hidden;
  }
  .slider {
    position: absolute;
    width: 50px;
    height: 50px;
    background: skyblue;
    border-radius: 8rpx;
    text-align: center;
    line-height: 50px;
    z-index: 3;
  }
  .slider-shadow {
    position: absolute;
    height: 50px;
    background: #fff;
    z-index: 2;
  }
  .slider-info {
    text-align: center;
    line-height: 50px;
    color: #666;
  }
  </style>