<template>
  <view class="spaceEnd" style="padding: 18rpx 0">
    <view
      v-for="btn in getOrderBtns(orderItem)"
      :key="btn.name"
      :class="btn.class"
      style="margin-left: 20rpx"
      @click.stop="btn.func(orderItem)"
      ><span>{{ btn.name }}</span></view
    >

    <payPopup
      v-if="showPayPopup"
      :order-detail="orderItem"
      @payNowFun="payNowFun"
      @close="showPayPopup = false"
    ></payPopup>

    <!-- 二次询问弹窗 -->
    <TipPanel
      v-if="showConfirmPop"
      @cancel="showConfirmPop = false"
      @confirm="active"
    >
      <view class="second-tip-panel-txt">您确认是否要操作？确认后不可撤销。 </view>
    </TipPanel>
  </view>
</template>
<script>
import { getOrderTeam } from '@/config/api/payOrder.js';
import { cancelUserOrder, deleteOrder } from '@/config/api/confirmOrder.js';
import payPopup from '@/components/popup/PayPopup';

import util from '@/utils/util';
import ChatMixin from '@/common/mixins/chat';

export default {
  components: {
    payPopup,
  },
  mixins: [ChatMixin],
  props: {
    orderItem: {
      type: Object,
      default: () => {},
    },
    type: {
      // 买家或卖家
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      showPayPopup: false,
      showConfirmPop: false,

      currentflag: '',
    };
  },
  methods: {
    getOrderBtns() {
      const { status } = this.orderItem;
      const isShow = status == 0 && this.type !== 2;
      const btns = [
        {
          name: '取消支付',
          func: (i) => this.cancelNow(i),
          show: isShow,
          class: 'gray-text-btn',
        },
        {
          name: '删除订单',
          func: (i) => this.delOrder(i),
          show: true,
          class: 'gray-text-btn',
        },
        {
          name: '立即支付',
          func: (i) => this.payOrderFunNow(i),
          show: isShow,
          class: 'kk-btn line',
        },
        {
          name: '联系客服',
          func: (i) => this.goChat(i),
          show: true,
          class: 'kk-btn orange',
        },
      ];
      return btns.filter((item) => item.show);
    },
    delOrder() {
      if (![4, 5, 12].includes(this.orderItem.status)) {
        uni.showToast({
          title: '流程中无法删除，请联系客服',
          icon: 'none',
        });
        return;
      }
      this.showConfirmPop = true;
      this.currentflag = 'del';
    },
    cancelNow() {
      this.showConfirmPop = true;
      this.currentflag = 'cancel';
    },
    active() {
      this.showConfirmPop = false
      if (this.currentflag === 'del') {
        deleteOrder(this.orderItem.id).then((res) => {
          if (res.code == 200) {
            this.$emit('refresh');
          }
        });
      } else if (this.currentflag === 'cancel') {
        cancelUserOrder({
          orderId: this.orderItem.id,
        }).then((res) => {
          if (res.code == 200) {
            this.$emit('refresh');
          }
        });
      }
    },
    goChat() {
      if (this.imUniLoading) {
        return;
      }
      this.imUniLoading = true;
      uni.showLoading({
        title: '加载中',
        mask: true,
      });
      const product = this.orderItem.orderItemList.find((ele) => {
        return ele.itemType === 0;
      });
      Promise.all([getOrderTeam(this.orderItem.id)])
        .then((values) => {
          const res = values[0];
          if (res.code == 200) {
            const imcode = res.data;
            const scene =util.isNumber(imcode)? `team`:'p2p';
            this.goChatPage({imcode, flag:scene, orderId:scene == 'p2p'?this.orderItem.id:''});
          }
        })
        .finally(() => {
          uni.hideLoading();
          setTimeout(() => {
            this.imUniLoading = false;
          }, 34);
        });
    },
    payNowFun(method, id) {
      console.log('aaaa', method, id);
      uni.redirectTo({
        url: '/pages/payOrder/payOrder?order_id=' + id + '&method=' + method,
      });
    },
    payOrderFunNow() {
      this.showPayPopup = true;
    },
  },
};
</script>
<style lang="scss" scoped>
.tip-title {
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
  margin-top: 40rpx;
}
</style>
