<template>
  <view class="card-box">
    <view class="item-head spaceBetween">
      <slot name="head-left">
        <view v-if="orderSn">
          <text v-if="orderTypeName"> {{ orderTypeName }}:</text>{{ orderSn }}
          <text @click.stop="copySn(orderSn)">
            <IconFont icon="copy" style="margin-left: 8rpx" />
          </text>
        </view>
        <view v-if="time"> {{ time | timeformat }} </view>
        <view v-else></view>
      </slot>
      <slot name="head-right"></slot>
    </view>
    <view class="item-content" @click.stop="goPage">
      <view class="spaceStart content-info">
        <view class="item-pic" >
          <image
            :src="pic || require('../../assets/imgs/product_img_default.png')"
            mode="scaleToFill"
            style="width: 100%;height: 100%;"
          />
        </view>
        <view class="item-detail">
          <slot name="detail">
            <view class="text_linThree" >{{ desc }}</view>
            <view class="text spaceBetween">
              <view>{{ name }}</view>
              <view v-if="price" class="price">
                <text v-if="historyPrice" class="historyPrice">
                  ¥{{ historyPrice }}
                </text>
                ¥{{ price }}</view
              >
            </view>
            <slot name="content"
          /></slot>
        </view>
      </view>
      <slot />
    </view>
  </view>
</template>
<script>
export default {
  props: {
    orderTypeName: {
      type: String,
      default: '',
    },
    orderSn: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    time: {
      type: String,
      default: '',
    },
    pic: {
      type: String,
      default: '',
    },
    desc: {
      type: String,
      default: '',
    },
    price: {
      type: Number,
      default: null,
    },
    historyPrice: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {};
  },
  methods: {
    copySn(str) {
      uni.setClipboardData({
        data: `${str}`,
        success: function () {},
      });
    },
    goPage() {
      this.$emit('toPage');
    },
  },
};
</script>
<style lang="scss" scoped>
.card-box {
  border-radius: 24rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.05);
  margin-bottom: 8rpx;

  .item-head {
    background: #fbf9f7;
    padding: 14rpx 44rpx;

    color: rgba(0, 0, 0, 0.4);
    font-size: 26rpx;
    font-weight: 400;
    letter-spacing: 0.48px;
  }

  .item-content {
    padding: 20rpx 20rpx 0;

    .content-info {
      align-items: flex-start;
      border-bottom: solid 2rpx rgba(0, 0, 0, 0.1);
      padding-bottom: 24rpx;

      height: 170rpx;
    }
  }

  .item-pic {
    width: 192rpx;
    height: 100%;
    flex-shrink: 0;
    border-radius: 12rpx;
    overflow: hidden;
    margin-right: 14rpx;

    image {
      height: 100%;
    }
  }

  .item-detail {
    flex: auto;
    color: #000;
    font-size: 24rpx;
    font-weight: 500;
    height: 100%;

    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .text {
      color: #969696;
      font-size: 20rpx;
      font-weight: 400;

      // margin-top: 44rpx;

      .price {
        color: $uni-color-primary;
        font-size: 28rpx;
        font-weight: 600;
      }

      .historyPrice {
        text-decoration: line-through;
        color: #969696;
        font-size: 20rpx;
        font-weight: 400;
      }
    }
  }

  .item-btns {
    // padding: 18rpx 12rpx;
  }
}
</style>
