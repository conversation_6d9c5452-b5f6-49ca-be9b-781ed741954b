<template>
  <view class="item-content">
    <view v-if="orderSn" style="margin-bottom: 8rpx;">订单编号:{{orderSn}}</view>
    <IconFont
      v-if="hasCloseIcon"
      :size="16"
      icon="close"
      class="close-icon"
      @click="doClose"
    />
    <view class="spaceStart content-info"  @click.stop="goPage">
      <view :class="isSmall ? 'sm' : ''" class="item-pic">
        <image :src="pic || defaultImg" mode="scaleToFill"
        style="width: 100%;height: 100%;" />
      </view>
      <view :class="isSmall ? 'sm' : ''" class="item-detail">
        <slot>
          <view :class="isSmall ? 'text_linTwo' : 'text_linThree'" :style="{paddingRight:hasCloseIcon && !orderSn?'26rpx':'0'}">{{
            desc
          }}</view>
          <view class="text spaceBetween">
            <view v-if="name">{{ name }}</view>
            <view v-if="price" class="price">
              <text v-if="historyPrice" class="historyPrice">
                ¥{{ historyPrice }}
              </text>
              ¥{{ price }}</view
            >
            <slot name="content" />
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    orderSn: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    pic: {
      type: String,
      default: '',
    },
    desc: {
      type: String,
      default: '',
    },
    price: {
      type: Number,
      default: null,
    },
    historyPrice: {
      type: Number,
      default: null,
    },
    hasCloseIcon: {
      type: Boolean,
      default: false,
    },
    isSmall: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultImg: require('../../assets/imgs/product_img_default.png'),
    };
  },
  methods: {
    doClose() {
      this.$emit('close');
    },
    goPage() {
      this.$emit('toPage');
    },
  },
};
</script>
<style lang="scss" scoped>
.item-content {
  padding: 20rpx 20rpx 0;
  background: #fff;
  margin-bottom: 8rpx;
  border-radius: 24rpx;
  position: relative;

  color: #000;
  font-size: 20rpx;

  .content-info {
    align-items: flex-start;
    padding-bottom: 24rpx;
  }
}

.close-icon {
  margin-left: 20rpx;
  position: absolute;
  right: 20rpx;
  top: 24rpx;
}

.item-pic {
  width: 192rpx;
  height: 170rpx;
  flex-shrink: 0;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 14rpx;
  border: solid 1rpx rgba(0, 0, 0, 0.1);

  &.sm {
    width: 140rpx;
    height: 140rpx;
    border-radius: 24rpx;
  }

  image {
    height: 100%;
  }
}

.item-detail {
  color: #000;
  font-size: 24rpx;
  font-weight: 500;
  flex: auto;

  &.sm {
    font-size: 20rpx;

    .text {
      margin-top: 18rpx;
      align-items: flex-end;

      .price {
        font-size: 24rpx;
      }
    }
  }

  .text {
    color: #969696;
    font-size: 20rpx;
    font-weight: 400;

    margin-top: 44rpx;

    .price {
      color: $uni-color-primary;
      font-size: 28rpx;
      font-weight: 600;
    }

    .historyPrice {
      text-decoration: line-through;
      color: #969696;
      font-size: 20rpx;
      font-weight: 400;
    }
  }
}
</style>
