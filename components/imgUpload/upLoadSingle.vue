<template>
  <view class="uploadList_box">
    <view v-if="urlPic" class="picUpload_wrapSmall">
      <img :src="urlPic" class="picUpload_pic" alt="" @click="preView" />
      <view class="delet_item" @click="deletPic">
        <IconFont :size="18" icon="del" />
      </view>
    </view>
    <view v-else class="picUpload_wrap uni-uploader__input-box" @click="chooseImg()">
    </view>
    <ModalAuth @authRefuse="isAuthVisible=false" @authSubmit="authSubmit" :isAuthVisible="isAuthVisible"/>
    <!-- <tnWaterMater
      :uploadList="uploadList"
      @pBackImage="backImage"
      ref="childWaterMater"
    ></tnWaterMater> -->
  </view>
</template>

<script>
import { ossUpload } from '@/js_sdk/jason-alioss-upload/oss.js';
import { base64ToPath } from 'image-tools';
// import tnWaterMater from '../waterMark/components/tn-waterMark/index.vue';

export default {
  components: {
    // tnWaterMater,
  },
  props: {
    urlPic: {
      type: String,
      default: '',
    },
    nameKey: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isAuthVisible:false,
      // uploadList: [1],
      isUploading: false, // 单张上传状态
    };
  },
  computed: {},
  methods: {
    preView() {
      uni.previewImage({
        current: this.urlPic,
        urls: [this.urlPic],
      });
    },
    deletPic() {
      this.$emit('upSuccsessSingle', '', this.nameKey);
    },
    async backImage(path) {
      // 加水印有几率，图片出来半个
      // base64ToPath(data).then(async (path) => {
      var that = this;
      const ext = path.split('.').pop() || '';
      const { success, data } = await ossUpload(path, ext);
      uni.hideLoading();
      if (success) {
        let newStr = data;
        that.$emit('upSuccsessSingle', newStr, that.nameKey);
      } else {
        uni.showToast({ icon: 'none', title: data });
      }
      // });
    },

    /**
     * 封面图片上传OSS-单张
     */
    chooseImg() {
      var that = this;
      var Manifest = plus.android.importClass('android.Manifest');
      var MainActivity = plus.android.runtimeMainActivity();
      let camera = MainActivity.checkSelfPermission(Manifest.permission.CAMERA);
      let storage = MainActivity.checkSelfPermission(
        Manifest.permission.READ_EXTERNAL_STORAGE,
      );
      if (camera != -1 && storage != -1) {
        uni.chooseImage({
        sourceType: ['camera', 'album'],
        sizeType: ['compressed'],
        count: 1,
        success: async (file) => {
          uni.showLoading({
            title: '图片上传中！',
          });
          // 上传OSS 组装数据
          let blobO = file.tempFiles[0]; // 文件file  file.tempFilePaths[0] 是 base 64
          let model = {};

          const result = await uni.compressImage({
            src: blobO.path,
            quality: 30,
          });
          this.backImage(result.tempFilePath);
          // model.url = result.tempFilePath;
          // model.index = 0;
          // model.watermark = [
          //   {
          //     type: 'image',
          //     path: '/static/water_pc.png',
          //     x: 0,
          //     y: 0,
          //     width: 271,
          //     height: 188,
          //     isRepeat: true,
          //     rotate: 0,
          //     repeatWidth: 3000,
          //     repeatHeight: 3000,
          //   },
          // ];
          // that.$refs.childWaterMater.addWaterMark(model);
        },
      });
      }else{
        this.isAuthVisible=true
        return
      }
      
    },
    authSubmit(){
      this.isAuthVisible=false
      plus.android.requestPermissions(
            [
              'android.permission.CAMERA',
              'android.permission.READ_EXTERNAL_STORAGE',
            ],
            (e) => {
              if (e.deniedPresent.length > 0 || e.deniedAlways.length > 0) {
                uni.showToast({
                  icon: 'none',
                  title: '获取拍摄、相册权限失败，请在设置中打开',
                });
              } else if (e.granted.length > 0) {
                uni.chooseImage({
                sourceType: ['camera', 'album'],
                sizeType: ['compressed'],
                count: 1,
                success: async (file) => {
                  uni.showLoading({
                    title: '图片上传中！',
                  });
                  // 上传OSS 组装数据
                  let blobO = file.tempFiles[0]; // 文件file  file.tempFilePaths[0] 是 base 64
                  let model = {};

                  const result = await uni.compressImage({
                    src: blobO.path,
                    quality: 30,
                  });
                  this.backImage(result.tempFilePath);
                },
              });
              } else {
              }
            },
          );
    },
  },
};
</script>

<style lang="scss" scoped>
$width: 144rpx;
$height: 144rpx;

.uploadList_box {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .picUpload_wrap,
  .picUpload_wrapSmall {
    position: relative;

    width: $width;
    height: $height;
    line-height: $height;
    background: #f7f7f7;
    border-radius: 24rpx;
    overflow: hidden;

    text-align: center;
    font-size: 28rpx;
    color: #969696;
  }
  .picUpload_wrap {
    border: 2rpx dashed #969696;

    &:before,
    &:after {
      content: ' ';
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      background-color: #969696;
    }

    &:before {
      width: 4rpx;
      height: 52rpx;
    }

    &:after {
      height: 4rpx;
      width: 52rpx;
    }
  }
  .picUpload_wrapSmall {
    border: 2rpx solid #969696;
    margin-right: 20rpx;

    .delet_item {
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      background: rgba(0, 0, 0, 0.5);
      z-index: 10;
      right: 0px;
      top: 0px;
      color: #fff;
      text-align: center;
      line-height: 52rpx;
      font-size: 44rpx;
      cursor: pointer;
    }

    .picUpload_pic {
      position: absolute;
      z-index: 1;
      left: 0px;
      right: 0px;
      top: 0px;
      bottom: 0px;
      width: 100%;
      height: 100%;
    }
  }
}
</style>