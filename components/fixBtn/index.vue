<template>
  <view :class="min ? 'min' : ''" class="fixBtn">
    <view v-if="!hideChat" class="chat" @click="goNews2">
      <view>
        <image class="chatImg" src="../../static/Chat.png" mode="scaleToFill" />
      </view>
      <view>消息</view>
      <view v-if="storeUnReadCountTx > 0" class="unread">{{
        storeUnReadCountTx < 99 ? storeUnReadCountTx : 99
      }}</view>
    </view>
    <view class="home" @click="goHome">
      <view>
        <image class="chatImg" src="../../static/gf.png" mode="scaleToFill" />
      </view>
      <view>返回首页</view>
    </view>
    <view class="home" @click="goTop">
      <view>
        <uni-icons type="arrow-up"></uni-icons>
      </view>
      <view>返回顶部</view>
    </view>
    <view class="spaceCenter arrow">
      <view @click="hideBtn">
        <image
          class="chatImg2"
          src="../../static/common_left_arrow.png"
          mode="scaleToFill"
        /><view>{{ hideTxt }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex';
import isLogin from '@/common/mixins/isLogin';
export default {
  computed: {
    ...mapGetters(['storeUnReadCountTx']),
    hideTxt() {
      return this.min ? '' : '收起';
    },
  },
  data() {
    return {
      min: false,
      hideChat: false,
    };
  },
  mounted() {
    if (!isLogin()) {
      this.hideChat = true;
    } else {
      this.hideChat = false;
    }
  },
  methods: {
    goHome() {
      uni.switchTab({
        url: '/pages/tabBar/home/<USER>',
      });
    },
    goNews2() {
      uni.navigateTo({
        url: '/pages/news2/news2',
      });
    },
    hideBtn() {
      this.min = !this.min;
    },
    goTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fixBtn {
  color: #999;
  position: fixed;
  bottom: 400rpx;
  right: 0;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  padding: 30rpx 10rpx 0 10rpx;
  z-index: 90;
  font-size: 22rpx;
  border: 1px solid rgba(2, 2, 2, 0.1);
  .arrow {
    padding-bottom: 20rpx;
  }
  .chat {
    text-align: center;
    margin-bottom: 20rpx;
    .unread {
      position: absolute;
      top: 10rpx;
      right: 6rpx;
      width: 40rpx;
      height: 40rpx;
      text-align: center;
      border-radius: 40rpx;
      line-height: 40rpx;
      color: #fff;
      background-color: #f00;
    }
  }
  .home {
    text-align: center;
    margin-bottom: 20rpx;
  }
}
.chatImg {
  width: 60rpx;
  height: 60rpx;
}
.chatImg2 {
  width: 20rpx;
  height: 20rpx;
  transform: rotate(180deg);
  margin-left: 10rpx;
}
.min {
  .chatImg2 {
    width: 20rpx;
    height: 20rpx;
    transform: rotate(0);
  }
  .chat {
    display: none;
  }
  .home {
    display: none;
  }
}
</style>
