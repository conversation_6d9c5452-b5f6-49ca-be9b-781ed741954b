<template>
  <view class="backHome">
    <view class="home" @click="goHome">
      <view>
        <image
          class="chatImg"
          src="../../static/imgs/tabBar/sale.png"
          mode="scaleToFill"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  mounted() {},
  methods: {
    goHome() {
      uni.switchTab({
        url: '/pages/tabBar/home/<USER>',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.backHome {
  color: #999;
  margin-top: 14rpx;
  margin-left: 20rpx;
  z-index: 3999;
  font-size: 22rpx;
  position: absolute;
  .chat {
    text-align: center;
    margin-bottom: 20rpx;
    .unread {
      position: absolute;
      top: 10rpx;
      right: 6rpx;
      width: 40rpx;
      height: 40rpx;
      text-align: center;
      border-radius: 40rpx;
      line-height: 40rpx;
      color: #fff;
      background-color: #f00;
    }
  }
  .home {
    text-align: center;
  }
}
.chatImg {
  width: 60rpx;
  height: 60rpx;
}
</style>
