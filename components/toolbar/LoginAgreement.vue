<template>
  <view class="radio_gropuSrearch">
    <view class="spaceStart" style="align-items: flex-start;" @click="open">
      <view>
        <IconFont
          v-show="!checkAgree"
          :size="15"
          icon="unchecked"
          class="check-box"
        />
      </view>
      <image
        v-show="checkAgree"
        src="../../assets/imgs/agreement_checked.svg"
        mode="heightFix"
        class="check-box"
        style="width:30px;height:30px;"
      />
      <view>
        未注册手机验证后自动创建看看账号网账户。点击登录/ 注册即表示您已阅读同意
        <text style="color: #ffb74a" @click.stop="rulePage">
          《看看账号网用户协议》
        </text>
      </view>
    </view>

    <!-- 服务协议弹窗 -->
    <TipPanel
      v-show="agreeMask"
      title="服务协议和隐私政策"
      confirm-text="同意"
      cancel-text="暂时不用"
      @cancel="unAgreeFun"
      @confirm="agreeFun"
    >
      <view style="margin-bottom: 40rpx">
        <view>
          请您务必审慎阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了向你提供即时通讯、内容分享等服务，我们需要收集你的设备信息、操作日志等个人信息。你可以在“设置”中查看、变更、删除个人信息并管理你的授权。
        </view>

        <view style="margin-top: 30rpx">
          你可阅读
          <text class="c-primary" @click.stop="rulePage">
            《看看账号网用户协议》
          </text>
          了解详细信息。如您同意，请点击“同意”开始接受我们的服务。
        </view>
      </view>
    </TipPanel>
  </view>
</template>
<script>
export default {
  data() {
    return {
      checkAgree: false,

      agreeMask: false,
    };
  },
  watch: {
    checkAgree(v) {
      // this.$emit('update:agree', v);
    },
  },
  methods: {
    /**
     * 弹层出现
     * **/
    open() {
      this.agreeMask = true;
    },
    unAgreeFun() {
      this.checkAgree = false;
      this.agreeMask = false;
      this.$emit('update:agree',  this.checkAgree);
    },
    agreeFun() {
      this.checkAgree = true;
      this.agreeMask = false;
      this.$emit('update:agree',  this.checkAgree);
    },
    rulePage() {
      uni.navigateTo({
        url: '/pages/noticeDetail/noticeDetail?from=helper&id=65',
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.radio_gropuSrearch {
  margin-bottom: 40rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #969696;
}
.check-box {
  margin-right: 10rpx;
  margin-top: -10rpx;
}
</style>
