<template>
  <view class="card-box">
    <view class="head">{{ title }}</view>
    <view v-if="list.length > 0">
      <view
        v-for="item in list"
        :key="item.name"
        class="list-item spaceBetween"
        @click="handelClick(item)"
      >
        <text>{{ item.name }}</text>

        <view>
          <IconFont icon="arrow-left" />
        </view>
      </view>
    </view>
    <view v-else-if="isLoading" class="loading-text">正在加载...</view>
    <KKEmpty v-else style="margin: 40rpx" />
  </view>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return{}
  },
  methods:{
    handelClick(item){
      this.$emit('click',item)
    }
  }
};
</script>

<style lang="scss" scoped>
.card-box {
  border-radius: 24rpx;
  background: #fff;
  overflow: hidden;
}
.head {
  padding: 20rpx 40rpx;
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 36rpx;
  font-weight: 400;
  background: #fbf9f7;
}
.loading-text {
  width: 100%;
  height: 200rpx;
  line-height: 200rpx;
  font-size: 26rpx;
  color: #969696;
  text-align: center;
}
.list-item {
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  font-size: 28rpx;
  font-weight: 500;
  color: #1b1b1b;
  padding: 32rpx 0;
  margin: 0 40rpx;
}
</style>
