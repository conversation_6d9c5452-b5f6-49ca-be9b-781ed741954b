<template>
  <view class="backHome" @click.stop="goHome">
    <IconFont :size="16" icon="home"/>
  </view>
</template>

<script>
export default {
  mounted() {},
  methods: {
    goHome() {
      uni.switchTab({
        url: '/pages/tabBar/home/<USER>',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.backHome {
  width: 24px;
  height: 24px;
  color: $uni-color-primary;
  text-align: center;
  line-height: 20px;
}
</style>
