<template>
  <view style="height: 96rpx" @touchmove.stop>
    <view class="select-box spaceStart">
      <view
        v-for="item in filterSetting"
        :class="[
          popupShow[item.key] ? 'select-comp-active' : '',
          item.selectValue && (item.selectValue===true || item.selectValue.length) ? 'weight' : '',
        ]"
        :key="item.key"
        class="select-comp spaceCenter"
        style="line-height: 48rpx;"
        @click="togglePopup(item.key)"
      >
        <view class="textOneLine">{{ getShowName(item) || item.name }}</view>
        <view :class="popupShow[item.key] ? 'rotate-180' : ''" style="margin-left: 8rpx">
           <IconFont :size="14"  icon="arrow-d"/>
         </view>
      </view>
    </view>

    <uni-popup
      v-for="item in filterSetting"
      :key="item.key"
      :ref="'popup' + item.key"
      type="top"
      style="z-index: 9"
      @change="(e) => popupOpenChange(e, item.key)"
      @maskClick="handleMaskClick(item.key)"
      @touchmove.stop
    >
      <slot :name="item.key + 'Panel'" :hidePopup="hidePopup">
        <SelectPanel
          :parent-options="item.parentOptions"
          :data-list="item.dataList"
          :scroll-h="item.scrollH"
          :style="popUpStyle"
          :value="item.selectValue"
          @change="(v) => handelSelect(v, item)"
        />
      </slot>
    </uni-popup>
  </view>
</template>
<script>
export default {
  props: {
    popUpStyle: {
      type: String,
      default: '',
    },
    filterSetting: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      popupShow: {}, // 弹窗打开标识位
    };
  },
  mounted() {},
  methods: {
    getShowName({ selectValue, dataList, parentOptions }) {
      if (!selectValue) {
        return '';
      }
      if (parentOptions && parentOptions.length) {
        const [parent, child] = selectValue.split('|');
        const parentInfo = parentOptions.find((e) => e.value === parent) || {};
        const childInfo = (parentInfo.childList || []).find(
          (e) => e.value === child,
        );
        if (!childInfo) {
          return parentInfo.label;
        }
        return childInfo.label;
      }
      const obj =
        (dataList || []).filter((e) => e.value === selectValue)[0] || {};
      return obj.label;
    },
    // 显示隐藏综合搜索
    togglePopup(key) {
      if (this.popupShow[key]) {
        this.hidePopup(key);
      } else {
        this.showPopup(key);
      }
    },
    showPopup(key) {
      // 先关闭其他弹窗
      for (let i in this.popupShow) {
        if (i !== key && this.popupShow[i]) {
          this.hidePopup(i);
        }
      }
      this.$refs['popup' + key][0].open('top');
      this.$set(this.popupShow, key, true);
    },
    hidePopup(key) {
      this.$refs['popup' + key][0].close();
      this.$set(this.popupShow, key, false);
    },
    popupOpenChange(e, key) {
      this.$set(this.popupShow, key, e.show);
    },
    // 点击遮罩
    handleMaskClick(key) {
      this.hidePopup(key);
    },
    handelSelect(v, item) {
      this.$emit('search', v, item.key);
      this.hidePopup(item.key);
    },
  },
};
</script>
<style lang="scss" scoped>
.select-box {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 90;

  padding: 0 40rpx;
  height: 96rpx;
  line-height: 96rpx;
  background-color: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);

  color: rgba(0, 0, 0, 0.4);
  font-size: 28rpx;
  font-weight: 400;

  .select-comp {
    width: 25%;
    text-align: center;
    flex-shrink: 1;
    flex: 1;

    &.weight {
      color: #222;
      font-weight: 500;
    }
  }
  .select-comp-active {
    color: #222;
    font-weight: 500;

    svg {
      transform: rotate(180deg);
    }
  }
}
.rotate-180 {
  transform: rotate(180deg);
}
</style>
