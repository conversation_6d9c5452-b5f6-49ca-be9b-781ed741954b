<template>
  <view>
    <view :class="clazz" class="listScroll_container">
      <trigger-indexList
        v-if="gameList.length"
        :list="gameList"
        :top-height="120"
        index-active-color="#FFB74A"
      >
        <template #default="{ indexList }">
          <!-- 最近浏览 -->
          <view
            v-if="needCash"
            id="cashFlagDateArrId"
            class="name-wrap"
             style="margin-right: 110rpx;border-bottom: solid 1rpx #ff7a00;"
          >
            <view class="title spaceBetween">
              <text>最近浏览</text>
              <text
                v-if="cashFlagDateArr.length "
                class="del-icon"
                @click="cleaCashGameData"
                >清除最近浏览 
                <IconFont :size="12" icon="delete"/>
              </text>
            </view>
            <view
              class="spaceStart"
              style="flex-wrap: wrap; align-items: flex-start；"
            >
              <view class="lineBody" @click="chooseAccount(nshJson)">
                <image
                  :src="nshJson.icon"
                  class="headImg"
                  mode="aspectFill"
                ></image>
                <view class="name textOneLine">{{ nshJson.name }}</view>
              </view>
              <view
                v-for="(child, childIndx) in cashFlagDateArr"
                v-if="childIndx < 8"
                :key="childIndx"
                class="lineBody"
                @click="chooseAccount(child)"
              >
                <image
                  :src="child.icon"
                  class="headImg"
                  mode="aspectFill"
                ></image>
                <view class="name textOneLine">{{ child.name }}</view>
              </view>
            </view>
          </view>
          <!-- 热门游戏开始 -->
          <view v-if="hotListGame.length" class="name-wrap">
            <view
              class="spaceStart gg"
              style="flex-wrap: wrap; align-items: flex-start"
            >
              <view
                v-for="(child, childIndx) in hotListGame"
                :key="childIndx"
                class="lineBody"
                @click="chooseAccount(child)"
              >
                <image
                  :src="child.icon"
                  class="headImg"
                  mode="aspectFill"
                ></image>
                <view class="name textOneLine">{{ child.name }}</view>
              </view>
            </view>
          </view>
          <!-- 数据index开始 -->
          <index-item
            v-for="(item, index) in indexList"
            v-if="item.data.length"
            :item="item"
            :key="index"
          >
            <template #lineBody>
              <view
                class="spaceStart"
                style="
                  flex-wrap: wrap;
                  align-items: flex-start;
                  margin-right: 60rpx;
                "
              >
                <view
                  v-for="(child, childIndx) in item.data"
                  :key="childIndx"
                  class="lineBody"
                  @click="chooseAccount(child)"
                >
                  <image
                    :src="child.icon"
                    class="headImg"
                    mode="aspectFill"
                  ></image>
                  <view class="name textOneLine"> {{ child.name }} </view>
                </view>
              </view>
            </template>
          </index-item>
        </template>
      </trigger-indexList>
    </view>
  </view>
</template>

<script>
import indexItem from '@/uni_modules/trigger-indexList/components/trigger-indexList_item/trigger-indexList_item.vue';
import { data } from '@/uni_modules/uview-ui/libs/mixin/mixin';
export default {
  name: 'GameList',
  components: { indexItem },
  props: {
    needCash: {
      type: Boolean,
      default: false,
    },
    clazz: {
      type: String,
      default: '',
    },
    gameListAll: {
      type: Array,
      default() {
        return [];
      },
    },
    gameList: {
      type: Array,
      default() {
        return [];
      },
    },
    hotListGame: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      cashFlagDateArr: uni.getStorageSync('cashFlagDateArr') || [],
    };
  },
  computed: {
    nshJson() {
      const findIt = this.gameListAll.find((v) => {
        return v.name === '逆水寒手游';
      });
      return findIt || {};
    },
  },
  mounted() {
  },
  methods: {
    cleaCashGameData() {
      const arr = this.cashFlagDateArr.filter(
        (item) => item.name === '逆水寒手游',
      );
      uni.setStorageSync('cashFlagDateArr', arr);
      this.cashFlagDateArr = arr;
    },
    // 缓存游戏数据
    cashGameData(date) {
      if (date.name === '逆水寒手游') {
        return;
      }
      var cashFlagDateArr = uni.getStorageSync('cashFlagDateArr');
      // 没有缓存的时候
      if (!cashFlagDateArr) {
        cashFlagDateArr = [];
        cashFlagDateArr.unshift(date);
        uni.setStorageSync('cashFlagDateArr', cashFlagDateArr);
      } else {
        // 有缓存分有何没有
        var num = cashFlagDateArr.findIndex((v) => {
          return v.id == date.id;
        });
        if (num == -1) {
          if (cashFlagDateArr.length >= 7) {
            cashFlagDateArr.pop();
          }
          cashFlagDateArr.unshift(date);
          uni.setStorageSync('cashFlagDateArr', cashFlagDateArr);
        }
      }
    },
    chooseAccount(item) {
      this.cashGameData(item);
      this.$emit('chooseAccount', item);
    },
  },
};
</script>

<style lang="scss" scoped>
.listScroll_container {
  box-sizing: border-box;
  height: 100%;
  overflow: hidden;
  position: absolute;
  left: 44rpx;
  right: 44rpx;
  bottom: 44rpx;
  top: 44rpx;
  height: calc(100% - 88rpx);

  /deep/ .lineHeadBox .lineHead {
    font-size: 24rpx;
    font-weight: 400;
    color: #1b1b1b;
  }
  /deep/ .lineBody .headImg {
    width: 92rpx;
    height: 92rpx;
    border-radius: 20rpx;
    margin: 0 auto;
  }
  .lineBody:nth-child(4n) {
    margin-right: 0;
  }

  .lineBody {
    text-align: center;
    box-sizing: border-box;
    flex-shrink: 0;
    width: 120rpx;
    font-size: 28rpx;
    font-weight: 500;
    margin-right: 10rpx;
    margin-bottom: 36rpx;
    color: $uni-color-subtitle;
    // .name {
    //   @include nomalEllipsis();
    // }
  }
  .name-wrap {
    color: $uni-color-subtitle;
    font-size: 24rpx;
    font-weight: 600;
  }

  .title {
    margin-bottom: 24rpx;
    line-height: 28rpx;

    .del-icon {
      color: rgba(0, 0, 0, 0.4);
      font-size: 22rpx;
      font-style: normal;
      font-weight: 400;
      letter-spacing: 0.4px;
      // padding-right: 14rpx;
    }
  }

  .name {
    font-size: 20rpx;
    color: $uni-color-subtitle;
    font-weight: 500;
  }
  /deep/ .root {
    .indexBox {
      z-index: 9;
      top: 0;
      transform: none;
      border-radius: 200px;
      background: #f9f6f3;
      gap: 8rpx;
      padding: 20rpx 10rpx;

      .index {
        height: 32rpx;
        width: 32rpx;
        font-weight: 400;
        color: $uni-color-paragraph;
      }
    }
  }
}
</style>
