export const AddFriendQrCodePrefix = 'io.openim.app/addFriend/'
export const AddGroupQrCodePrefix = 'io.openim.app/joinGroup/'

export const ChatingFooterActionTypes = {
	Album: 'Album',
	Camera: 'Camera',
	Call: 'Call',
	File: 'File',
	Card: 'Card',
	Location: 'Location'
}

export const MessageMenuTypes = {
	Copy: 'Copy',
	Del: 'Del',
	Forward: 'Forward',
	Reply: 'Reply',
	Revoke: 'Revoke',
	Multiple: 'Multiple'
}

export const ContactMenuTypes = {
	NewFriend: 'NewFriend',
	NewGroup: 'NewGroup',
	MyFriend: 'MyFriend',
	MyGroup: 'MyGroup',
	Lable: 'Lable'
}

export const GroupMemberListTypes = {
	Preview: 'Preview',
	Transfer: 'Transfer',
	Kickout: 'Kickout',
	ChooseAt: 'ChooseAt',
	CallInvite: 'CallInvite'
}

export const ContactChooseTypes = {
	Invite: 'Invite',
	GetList: 'GetList'
}

export const UpdateMessageTypes = {
	Overall: 'Overall',
	KeyWords: 'KeyWords',
}

export const SmsUserFor = {
	Register: 1,
	Reset: 2,
	Login: 3,
}

export const CommonIsAllow = {
	Allow: 1,
	NotAllow: 2
}

export const CustomMessageStatus = {
		Success: 'success',
		Cancel: 'cancel',
		Canceled: 'canceled',
		Refuse: 'refuse',
		Refused: 'refused',
		Timeout: 'timeout',
		AccessByOther: 'accessByOther'
	}