// yx start
import RootStore from '@xkit-yx/im-store';
import { NimKitCore } from '@xkit-yx/core-kit/dist/uniapp-nim-core';
import { getMsgContentTipByType } from '../../pages/NEUIKit/utils/msg';
import { getUniPlatform } from '../../pages/NEUIKit/utils';
import { getImInfo } from '@/config/api/iminfo.js';
import _ from 'lodash';
// #ifdef APP-PLUS
const nimPushPlugin = uni.requireNativePlugin('NIMUniPlugin-PluginModule');
// #endif
// yx end
uni.doNotConnect = 0;
uni.hasInit = false;
import { autorun } from 'mobx';
import Vue from 'vue';

const initIM = (yximtoken) => {
  return new Promise(async (resolve, reject) => {
    if (uni.hasInit) {
      resolve(true);
    }
    // yx start
    const isWeixinApp = getUniPlatform() === 'mp-weixin';
    // @ts-ignore
    const funcOptions = {};
    const initOptions = {
      'appkey': 'b14909500c44a20d3b8b252fab213ca2',
      'account': yximtoken.account, // 请填写你的account
      'token': yximtoken.token, // 请填写你的token
      'authType': 1,
      'lbsUrls': isWeixinApp
        ? ['https://lbs.netease.im/lbs/wxwebconf.jsp']
        : ['https://lbs.netease.im/lbs/webconf.jsp'],
      'linkUrl': isWeixinApp ? 'wlnimsc0.netease.im' : 'weblink.netease.im',
      /**
       * 使用固定设备ID，
       */
      isFixedDeviceId: true,
      // "reconnectionAttempts": 5,
      debugLevel: 'off',
    };
    const nim = (uni.$UIKitNIM = new NimKitCore({
      initOptions,
      funcOptions,
      platform: 'UniApp',
    }));
    // @ts-ignore
    const store = (uni.$UIKitStore = new RootStore(nim, {
      addFriendNeedVerify: false,
      teamBeInviteMode: 'noVerify',
      teamJoinMode: 'noVerify',
      teamUpdateExtMode: 'all',
      teamUpdateTeamMode: 'all',
      teamInviteMode: 'all',
      teamMsgReceiptVisible: true,
      sendMsgBefore: async (options, type) => {
        const store = uni.$UIKitStore;
        const pushContent = getMsgContentTipByType({
          body: options.body,
          type,
        });
        const yxAitMsg = options.ext
          ? options.ext.yxAitMsg
          : { forcePushIDsList: '[]', needForcePush: false };

        // 如果是 at 消息，需要走离线强推
        const { forcePushIDsList, needForcePush } = yxAitMsg
          ? // @ts-ignore
            store.msgStore._formatExtAitToPushInfo(yxAitMsg, options.body)
          : { forcePushIDsList: '[]', needForcePush: false };

        const pushPayload = JSON.stringify({
          pushTitle: '您收到一条新的消息提醒，请注意查看',
        });
        const pushInfo = {
          needPush: true,
          needPushBadge: true,
          pushPayload,
          pushContent,
          needForcePush,
          forcePushIDsList,
          forcePushContent: pushContent,
        };

        let antiSpamInfo = {};
        if (options.ext) {
          try {
            let ext = JSON.parse(options.ext);
            if (ext.noyidun == 1) {
              antiSpamInfo.antiSpamUsingYidun = false;
            }
          } catch (e) {}
        }
        const result = { ...options, antiSpamInfo, pushInfo };
        return result;
      },
    }));
    nim.on('willReconnect', function (obj) {
      // todo 找云信的要 302 才重连
      if (uni.doNotConnect < 3) {
        refreshImToken();
      }
    });
    nim.on('disconnect', function (obj) {
      // todo 找云信的要 302 才重连
      if (uni.doNotConnect < 3) {
        refreshImToken();
      }
    });
    autorun(() => {
      Vue.prototype.$store.dispatch(
        'setImUnreadCount',
        uni.$UIKitStore.uiStore.sessionUnread,
      );
    });
    // #ifdef APP-PLUS
    // 注册推送
    nim.getNIM().offlinePush.setOfflinePushConfig({
      plugin: nimPushPlugin,
      authConfig: {
        // xiaomi
        xmAppId: '2882303761520292070',
        xmAppKey: '5772029278070',
        xmCertificateName: 'KIT_UNIAPP_MI_PUSH',

        // huawei
        hwAppId: '110130649',
        hwCertificateName: 'KIT_UNIAPP_HW_PUSH',

        // oppo
        oppoAppId: '31503871',
        oppoAppKey: 'c4de881167974d7ab9e6239f97c8f564',
        oppoAppSecret: '581b74805a2c43b1b38aa28a152b4a1b',
        oppoCertificateName: 'KIT_UNIAPP_OPPO_PUSH',

        /**
         * 注意vivo的appid和appkey需要同时在此处，以及manifest.json(即插件参数配置)中配置
         */
        vivoAppId: '105677266',
        vivoAppKey: '7cff82ca20a29bee0514c5092e909550',
        vivoCertificateName: 'KIT_UNIAPP_VIVO_PUSH',

        // fcm
        fcmCertificateName: 'KIT_UNIAPP_FCM_PUSH',

        // meizu
        mzAppId: '',
        mzAppKey: '',
        mzCertificateName: 'KIT_UNIAPP_MZ_PUSH',

        // iOS
        apnsCertificateName: 'dis_im_uniapp',
      },
    });
    // #endif
    nim
      .connect()
      .then((res) => {
        uni.doNotConnect = 0;
        uni.$IMok.setImok(true);
        resolve(true);
      })
      .catch((e) => {
        reject();
      });

    autorun(() => {
      let sessionList = _.cloneDeep(uni.$UIKitStore?.uiStore?.sessionList)?.map(
        (session, index) => {
          return {
            ...session,
            renderKey: session.id,
          };
        },
      );
      let accounts = [];
      sessionList.forEach((ele) => {
        const { to: account } = ele;
        accounts.push(account);
      });

      // 调用云信私有方法，为了预加载用户昵称
      uni.$UIKitStore?.userStore?._getUserInfos(accounts);
    });
    // #ifdef H5
    uni?.$UIKitNIM?.on('msg', (msg) => {
      const { from } = msg;
      let curPathName = location.pathname;
      const myAccount = uni.$UIKitStore?.userStore.myUserInfo.account;
      if (
        curPathName != '/pages/NEUIKit/pages/Chat/index' &&
        myAccount != from
      ) {
        try {
          if (
            !(msg.type === 'custom' && msg?.attach?.type === 'time') &&
            !(msg.type === 'custom' && msg?.attach?.type === 'reCallMsg') &&
            !(msg.type === 'custom' && msg?.attach?.type === 'beReCallMsg') &&
            msg.type != 'notification' &&
            msg.type != 'tip'
          ) {
            // 其他消息需要显示推送
            const title = '您有新的消息！';
            const options = {
              body: '亲爱的用户，您有新的待处理消息，请查看。',
              icon: 'https://images2.kkzhw.com/mall/images/********/p3og25_1723131039596.png',
              data: {
                'url': location.origin + '/pages/tabBar/news/news',
                'message_id': 'your_internal_unique_message_id_for_tracking',
              },
            };
            navigator.serviceWorker.ready.then(async function (serviceWorker) {
              await serviceWorker.showNotification(title, options);
            });
          }
        } catch (e) {
          console.log(e);
        }
      }
    });
    // #endif
    uni.hasInit = true;
  });
};

const refreshImToken = () => {
  getImInfo()
    .then(async (res) => {
      // 刷新token
      if (res && res.code === 200) {
        // token 拿到了就加 1，3 次都连不上说明不是网络问题，可能是限流等其他问题
        uni.doNotConnect++;
        const { accid, token } = res.data;
        uni.setStorageSync(
          'yximtoken',
          JSON.stringify({ 'account': accid, 'token': token }),
        );
        uni.$UIKitNIM.nim.setOptions({
          token: token,
        });
        uni.$UIKitNIM.nim
          .connect()
          .then((res) => {
            uni.$IMok.setImok(true);
            uni.doNotConnect = 0;
          })
          .catch((err) => {
            // const { code = '' } = err;
            // if (code != 302) {
            // 如果刷新token 后，连接失败不是 302
            // uni.doNotConnect++;
            // }
          });
      }
    })
    .catch(() => {});
};

export default function isIMToken() {
  return new Promise(async (resolve, reject) => {
    if (uni.hasInit && uni.getStorageSync('yximtoken')) {
      resolve(true);
    } else {
      getImInfo()
        .then(async (res) => {
          if (res.code === 200) {
            const { accid, token } = res.data;
            uni.setStorageSync(
              'yximtoken',
              JSON.stringify({ 'account': accid, 'token': token }),
            );
            let yximtoken = uni.getStorageSync('yximtoken');
            // 保证初始化只执行一次
            await initIM(JSON.parse(yximtoken));
            return resolve(yximtoken);
          }
        })
        .catch(() => {
          // 获取失败
          uni.hasInit = false;
          reject();
        });
    }
  });
}
