export default function isLogin() {
  // uni.setStorageSync('token', '842298370473997c284a26244b1ac237')
  var token = uni.getStorageSync('token');
  if (token) {
    return true;
    // checkToken().then((res) => {
    // 	if(res.ret == 0){
    // 		return true;
    // 	}else{
    // 		uni.clearStorageSync('token');
    // 		uni.clearStorageSync('bind_phone');
    // 		return false;
    // 	}
    // })
  } else {
    return false;
  }
}
