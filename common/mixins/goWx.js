import { getWxQrcode } from '@/config/api/main.js';
export default {
  data() {
    return {};
  },
  mounted() {},
  methods: {
    handleBindWx() {
      getWxQrcode().then((res) => {
        if (res.code === 200) {
          const ticket = res.data.ticket;
          const url = `weixin://dl/business/?appid=wx7479a2c489dca6e7&path=pages/officialAccounts/officialAccounts&query=ticket=${ticket}`;
          
          // #ifndef APP-PLUS
          window.location.href = url  
          // #endif

          // #ifdef APP-PLUS
          plus.runtime.openURL(url);
          // #endif
        }
      });
    },
  },
};
