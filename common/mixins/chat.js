import { getMemberHisKFList, m2kfTalk, getKfList } from '@/config/api/kf.js';

export default {
  methods: {
    // @flag： p2p、team
    goChatPage({ imcode, flag = 'p2p', productCategoryId, productId,orderId}) {
      if (imcode) {
        const sessionId = `${flag}-${imcode}`;

        if (productCategoryId) {
          m2kfTalk({
            cateId: productCategoryId,
            kfIM: imcode,
          });
        }

        if (uni.$UIKitStore.sessionStore.sessions.get(sessionId)) {
          uni.$UIKitStore.uiStore.selectSession(sessionId);
        } else {
          uni.$UIKitStore.sessionStore.insertSessionActive(flag, imcode);
        }

        let params=`?sessionId=${sessionId}`
        if(productId){
          params+= `&productId=${productId}`
        }
        if(orderId){
          params+= `&orderId=${orderId}`
        }
        
        uni.navigateTo({
          url: `/pages/NEUIKit/pages/Chat/index${params}`,
        });
      } else {
        uni.switchTab({ url: '/pages/tabBar/news/news' });
      }
    },
  },
};
