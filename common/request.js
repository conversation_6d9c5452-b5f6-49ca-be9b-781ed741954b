var Fly = require('flyio/dist/npm/wx');
var request = new Fly();
import util from '@/utils/util';

import _ from 'lodash';

const debouncedMsg = _.debounce(
  (err, type) => {
    errorPrompt(err, type);
  },
  2000,
  { 'leading': true, 'maxWait': 5000 },
);
const errorPrompt = (err, type) => {
  if (type == 1) {
    // #ifdef MP-WEIXIN
    wx.showToast({
      title: err.message || '获取数据失败.',
      icon: 'none',
    });
    // #endif

    // #ifdef APP-PLUS
    // #endif
    uni.showToast({
      title: err.message || '获取数据失败.',
      icon: 'none',
    });
  } else if (type == 2) {
    let status = err.status || err.code || '';
    if (status == 0) {
      // 断网不提示错误，debouncedMsg会把这段时间的错误提示显示
      return;
    }
    let data = '获取数据失败';
    if (status.toString().indexOf('5') == 0) {
      data = '系统维护中，请稍后再试';
    }
    // #ifdef MP-WEIXIN
    wx.showToast({
      title: data,
      icon: 'none',
    });
    // #endif

    uni.showToast({
      title: data,
      icon: 'none',
    });
  }
};

request.interceptors.request.use((request) => {
  if (request.method === 'POST') {
    if (request.ext) {
      request.url += '?' + util.obj2params(request.ext);
    }
  }
  request.timeout = 20000;
  request.headers = {
    'Content-Type': 'application/json',
    Authorization: uni.getStorageSync('token'),
    'Kk-prt-from': 'aos',
    'kk-oprt-from': 'dy2w',
  };
  return request;
});

request.interceptors.response.use(
  (response, promise) => {
    if (
      response.request &&
      response.request.body &&
      response.request.body.noerrorshow == '1'
    ) {
      response.data.code = 200;
    }
    if (response.data.code == 200) {
      return promise.resolve(response.data);
    } else if (response.data.code == 401) {
      uni.showToast({
        title: '登录过期，请重新登录',
        icon: 'none',
      });
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login',
        });
      }, 1000);
    } else {
      // 1代表网络正常，服务器客户端错误
      if (response.request.url.indexOf('member/product/smsCode') == -1) {
        debouncedMsg(response.data, 1);
      }
      return promise.resolve(response.data);
    }
  },
  (err, promise) => {
    debouncedMsg(err, 2); //2代表网络正常
    return promise.resolve(err.data);
  },
);
export default request;
